{"manifest_version": 3, "name": "ChefAI - Advanced Recipe Generator", "version": "1.0.0", "description": "Professional AI-powered recipe generator with smart search, glassmorphism UI, and advanced culinary features", "permissions": ["storage", "activeTab", "scripting", "background", "notifications", "alarms", "identity", "webRequest"], "host_permissions": ["https://*/*", "http://*/*", "https://openrouter.ai/*", "https://api.openai.com/*", "https://api.anthropic.com/*", "https://generativelanguage.googleapis.com/*"], "background": {"service_worker": "background.js", "action": {"default_popup": "popup.html", "default_title": "ChefAI - AI Recipe Generator", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["src/content/enhanced-injector.js", "content.js"], "run_at": "document_end", "css": ["src/styles/sidebar.css", "src/styles/dashboard.css", "src/styles/customization.css", "content.css"]}], "action": {"default_popup": "popup.html", "default_title": "ChefAI Recipe Generator", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "options_page": "options.html", "web_accessible_resources": [{"resources": ["assets/*", "icons/*", "src/styles/*", "src/components/*", "src/services/*", "src/content/*", "dist/*"], "matches": ["<all_urls>"]}]}
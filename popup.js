// ChefAI Enhanced Popup Script
document.addEventListener('DOMContentLoaded', function() {
  console.log('ChefAI Popup loaded');

  // Initialize popup
  initializePopup();
});

function initializePopup() {
  // Get DOM elements
  const generateBtn = document.getElementById('generateBtn');
  const ingredientsInput = document.getElementById('ingredientsInput');
  const recipeDisplay = document.getElementById('recipeDisplay');
  const messageDiv = document.getElementById('message');
  const recentRecipesDiv = document.getElementById('recentRecipes');
  const openDashboardBtn = document.getElementById('openDashboardBtn');
  const openSidebarBtn = document.getElementById('openSidebarBtn');
  const aiStatus = document.getElementById('aiStatus');
  const settingsStatus = document.getElementById('settingsStatus');

  // Set up event listeners
  if (generateBtn) {
    generateBtn.addEventListener('click', handleGenerateRecipe);
  }

  if (openDashboardBtn) {
    openDashboardBtn.addEventListener('click', () => openInterface('dashboard'));
  }

  if (openSidebarBtn) {
    openSidebarBtn.addEventListener('click', () => openInterface('sidebar'));
  }

  if (ingredientsInput) {
    ingredientsInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        handleGenerateRecipe();
      }
    });
  }

  // Load initial data
  loadRecentRecipes();
  checkSystemStatus();
}

async function handleGenerateRecipe() {
  const ingredientsInput = document.getElementById('ingredientsInput');
  const generateBtn = document.getElementById('generateBtn');
  const messageDiv = document.getElementById('message');

  if (!ingredientsInput || !generateBtn) return;

  const ingredients = ingredientsInput.value.trim();
  if (!ingredients) {
    showMessage('Please enter some ingredients first!', 'warning');
    return;
  }

  // Disable button and show loading
  generateBtn.disabled = true;
  generateBtn.textContent = '⏳ Generating...';

  try {
    // Send message to content script to open interface and generate recipe
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    await chrome.tabs.sendMessage(tab.id, {
      action: 'showInterface',
      mode: 'sidebar',
      data: { ingredients }
    });

    showMessage('Opening recipe generator...', 'success');

    // Close popup after a short delay
    setTimeout(() => {
      window.close();
    }, 1000);

  } catch (error) {
    console.error('Error generating recipe:', error);
    showMessage('Failed to open recipe generator. Please try again.', 'error');
  } finally {
    // Re-enable button
    generateBtn.disabled = false;
    generateBtn.textContent = '✨ Generate Recipe';
  }
}

async function openInterface(mode = 'dashboard') {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

    await chrome.tabs.sendMessage(tab.id, {
      action: 'showInterface',
      mode: mode
    });

    // Close popup
    window.close();

  } catch (error) {
    console.error('Error opening interface:', error);
    showMessage(`Failed to open ${mode}. Please refresh the page and try again.`, 'error');
  }
}

function showMessage(text, type = 'info') {
  const messageDiv = document.getElementById('message');
  if (!messageDiv) return;

  messageDiv.textContent = text;
  messageDiv.className = `message ${type}`;
  messageDiv.style.display = 'block';

  // Auto-hide after 3 seconds
  setTimeout(() => {
    messageDiv.style.display = 'none';
  }, 3000);
}

async function loadRecentRecipes() {
  const recentRecipesDiv = document.getElementById('recentRecipes');
  if (!recentRecipesDiv) return;

  try {
    // Get recent recipes from storage
    const result = await chrome.storage.local.get(['chefai_recipes']);
    const recipes = result.chefai_recipes || [];

    if (recipes.length === 0) {
      recentRecipesDiv.innerHTML = '<p class="no-recipes">No recent recipes. Generate your first recipe!</p>';
      return;
    }

    // Show last 3 recipes
    const recentRecipes = recipes.slice(-3).reverse();
    recentRecipesDiv.innerHTML = recentRecipes.map(recipe => `
      <div class="recipe-item">
        <div class="recipe-title">${recipe.title || 'Untitled Recipe'}</div>
        <div class="recipe-date">${new Date(recipe.createdAt).toLocaleDateString()}</div>
      </div>
    `).join('');

  } catch (error) {
    console.error('Error loading recent recipes:', error);
    recentRecipesDiv.innerHTML = '<p class="no-recipes">Error loading recipes</p>';
  }
}

async function checkSystemStatus() {
  const aiStatus = document.getElementById('aiStatus');
  const settingsStatus = document.getElementById('settingsStatus');

  try {
    // Check if LLM is configured
    const result = await chrome.storage.local.get(['chefai_llm_config', 'chefai_advanced_settings']);

    const llmConfig = result.chefai_llm_config;
    const advancedSettings = result.chefai_advanced_settings;

    // Update AI status
    if (aiStatus) {
      if (llmConfig && llmConfig.apiKey) {
        aiStatus.textContent = 'Ready';
        aiStatus.className = 'status-value success';
      } else {
        aiStatus.textContent = 'Not Configured';
        aiStatus.className = 'status-value warning';
      }
    }

    // Update settings status
    if (settingsStatus) {
      if (advancedSettings) {
        settingsStatus.textContent = 'Configured';
        settingsStatus.className = 'status-value success';
      } else {
        settingsStatus.textContent = 'Default';
        settingsStatus.className = 'status-value info';
      }
    }

  } catch (error) {
    console.error('Error checking system status:', error);

    if (aiStatus) {
      aiStatus.textContent = 'Error';
      aiStatus.className = 'status-value error';
    }

    if (settingsStatus) {
      settingsStatus.textContent = 'Error';
      settingsStatus.className = 'status-value error';
    }
  }
}
  const generateBtn = document.getElementById('generateBtn');
  const ingredientsInput = document.getElementById('ingredientsInput');
  const openFullAppBtn = document.getElementById('openFullAppBtn');

  if (generateBtn) {
    generateBtn.addEventListener('click', generateRecipe);
  }

  if (openFullAppBtn) {
    openFullAppBtn.addEventListener('click', openFullApp);
  }

  if (ingredientsInput) {
    ingredientsInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        generateRecipe();
      }
    });
  }

  // Load recent recipes
  loadRecentRecipes();
}

function generateRecipe() {
  const ingredientsInput = document.getElementById('ingredientsInput');
  const ingredients = ingredientsInput ? ingredientsInput.value : '';

  if (!ingredients.trim()) {
    showMessage('Please enter some ingredients!', 'error');
    return;
  }

  showMessage('Generating recipe...', 'info');

  // Send message to background script
  chrome.runtime.sendMessage({
    type: 'GENERATE_RECIPE',
    ingredients: ingredients
  }, function(response) {
    if (response && response.success) {
      showMessage('Recipe generated successfully!', 'success');
      displayRecipe(response.recipe);
    } else {
      showMessage('Failed to generate recipe. Please try again.', 'error');
    }
  });
}

function displayRecipe(recipe) {
  const recipeDisplay = document.getElementById('recipeDisplay');
  if (recipeDisplay && recipe) {
    recipeDisplay.innerHTML = `
      <div class="recipe-card">
        <h3>${recipe.title}</h3>
        <p>${recipe.description}</p>
        <div class="recipe-meta">
          <span>📝 ${recipe.ingredients.length} ingredients</span>
          <span>👨‍🍳 ${recipe.instructions.length} steps</span>
        </div>
      </div>
    `;
    recipeDisplay.style.display = 'block';
  }
}

function openFullApp() {
  chrome.tabs.create({
    url: chrome.runtime.getURL('options.html')
  });
}

function loadRecentRecipes() {
  chrome.storage.local.get(['recentRecipes'], function(result) {
    const recentRecipes = result.recentRecipes || [];
    const recentList = document.getElementById('recentRecipes');

    if (recentList) {
      if (recentRecipes.length === 0) {
        recentList.innerHTML = '<p class="no-recipes">No recent recipes. Generate your first recipe!</p>';
      } else {
        recentList.innerHTML = recentRecipes.slice(0, 3).map(recipe => `
          <div class="recent-recipe-item">
            <span class="recipe-title">${recipe.title}</span>
            <span class="recipe-time">${recipe.createdAt}</span>
          </div>
        `).join('');
      }
    }
  });
}

function showMessage(message, type) {
  const messageDiv = document.getElementById('message');
  if (messageDiv) {
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';

    setTimeout(() => {
      messageDiv.style.display = 'none';
    }, 3000);
  }
}
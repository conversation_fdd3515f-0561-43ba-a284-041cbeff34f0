// ChefAI Popup Script
document.addEventListener('DOMContentLoaded', function() {
  console.log('ChefAI Popup loaded');

  // Initialize popup
  initializePopup();
});

function initializePopup() {
  // Set up event listeners
  const generateBtn = document.getElementById('generateBtn');
  const ingredientsInput = document.getElementById('ingredientsInput');
  const openFullAppBtn = document.getElementById('openFullAppBtn');

  if (generateBtn) {
    generateBtn.addEventListener('click', generateRecipe);
  }

  if (openFullAppBtn) {
    openFullAppBtn.addEventListener('click', openFullApp);
  }

  if (ingredientsInput) {
    ingredientsInput.addEventListener('keypress', function(e) {
      if (e.key === 'Enter') {
        generateRecipe();
      }
    });
  }

  // Load recent recipes
  loadRecentRecipes();
}

function generateRecipe() {
  const ingredientsInput = document.getElementById('ingredientsInput');
  const ingredients = ingredientsInput ? ingredientsInput.value : '';

  if (!ingredients.trim()) {
    showMessage('Please enter some ingredients!', 'error');
    return;
  }

  showMessage('Generating recipe...', 'info');

  // Send message to background script
  chrome.runtime.sendMessage({
    type: 'GENERATE_RECIPE',
    ingredients: ingredients
  }, function(response) {
    if (response && response.success) {
      showMessage('Recipe generated successfully!', 'success');
      displayRecipe(response.recipe);
    } else {
      showMessage('Failed to generate recipe. Please try again.', 'error');
    }
  });
}

function displayRecipe(recipe) {
  const recipeDisplay = document.getElementById('recipeDisplay');
  if (recipeDisplay && recipe) {
    recipeDisplay.innerHTML = `
      <div class="recipe-card">
        <h3>${recipe.title}</h3>
        <p>${recipe.description}</p>
        <div class="recipe-meta">
          <span>📝 ${recipe.ingredients.length} ingredients</span>
          <span>👨‍🍳 ${recipe.instructions.length} steps</span>
        </div>
      </div>
    `;
    recipeDisplay.style.display = 'block';
  }
}

function openFullApp() {
  chrome.tabs.create({
    url: chrome.runtime.getURL('options.html')
  });
}

function loadRecentRecipes() {
  chrome.storage.local.get(['recentRecipes'], function(result) {
    const recentRecipes = result.recentRecipes || [];
    const recentList = document.getElementById('recentRecipes');

    if (recentList) {
      if (recentRecipes.length === 0) {
        recentList.innerHTML = '<p class="no-recipes">No recent recipes. Generate your first recipe!</p>';
      } else {
        recentList.innerHTML = recentRecipes.slice(0, 3).map(recipe => `
          <div class="recent-recipe-item">
            <span class="recipe-title">${recipe.title}</span>
            <span class="recipe-time">${recipe.createdAt}</span>
          </div>
        `).join('');
      }
    }
  });
}

function showMessage(message, type) {
  const messageDiv = document.getElementById('message');
  if (messageDiv) {
    messageDiv.textContent = message;
    messageDiv.className = `message ${type}`;
    messageDiv.style.display = 'block';

    setTimeout(() => {
      messageDiv.style.display = 'none';
    }, 3000);
  }
}
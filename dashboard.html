<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChefAI Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #2c3e50;
            color: #ecf0f1;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: #34495e;
            padding: 20px;
            border-bottom: 1px solid #4a5f7a;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #ecf0f1;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            background: #3498db;
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-secondary {
            background: #95a5a6;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .main-content {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .dashboard-icon {
            font-size: 64px;
            margin-bottom: 24px;
            opacity: 0.7;
        }
        
        .dashboard-title {
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #ecf0f1;
        }
        
        .dashboard-description {
            font-size: 16px;
            color: #95a5a6;
            margin-bottom: 32px;
            max-width: 600px;
            line-height: 1.6;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            max-width: 1000px;
            width: 100%;
            margin-top: 40px;
        }
        
        .feature-card {
            background: #34495e;
            border: 1px solid #4a5f7a;
            border-radius: 8px;
            padding: 24px;
            text-align: left;
            transition: all 0.2s ease;
        }
        
        .feature-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 16px;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #ecf0f1;
            margin-bottom: 8px;
        }
        
        .feature-description {
            font-size: 14px;
            color: #95a5a6;
            line-height: 1.5;
        }
        
        .status-bar {
            background: #34495e;
            border-top: 1px solid #4a5f7a;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #95a5a6;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
        }
        
        .status-dot.warning {
            background: #f39c12;
        }
        
        .status-dot.error {
            background: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            🍳 ChefAI Dashboard
        </h1>
        <div class="header-actions">
            <button class="btn btn-secondary" onclick="openSettings()">Settings</button>
            <button class="btn" onclick="openExtension()">Open Extension</button>
        </div>
    </div>
    
    <div class="main-content">
        <div class="dashboard-icon">🎛️</div>
        <h2 class="dashboard-title">Welcome to ChefAI Dashboard</h2>
        <p class="dashboard-description">
            Your central hub for managing AI-powered recipe generation. Monitor performance, 
            configure advanced settings, and explore powerful features to enhance your culinary experience.
        </p>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">🤖</div>
                <h3 class="feature-title">AI Models</h3>
                <p class="feature-description">
                    Configure and fine-tune multiple AI models for different tasks including recipe planning, 
                    navigation, and validation with precise temperature and Top-P controls.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🍳</div>
                <h3 class="feature-title">Recipe Generation</h3>
                <p class="feature-description">
                    Generate personalized recipes using advanced AI with support for dietary restrictions, 
                    cuisine preferences, and cooking skill levels.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔒</div>
                <h3 class="feature-title">Security & Privacy</h3>
                <p class="feature-description">
                    Advanced firewall settings to control which websites can access ChefAI features, 
                    ensuring your privacy and security while browsing.
                </p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">Performance</h3>
                <p class="feature-description">
                    Real-time monitoring of API usage, response times, and system performance 
                    to ensure optimal recipe generation experience.
                </p>
            </div>
        </div>
    </div>
    
    <div class="status-bar">
        <div class="status-item">
            <div class="status-dot"></div>
            <span>Extension Active</span>
        </div>
        <div class="status-item">
            <div class="status-dot" id="api-status"></div>
            <span id="api-status-text">Checking API...</span>
        </div>
        <div class="status-item">
            <span>Version 1.0.0</span>
        </div>
    </div>
    
    <script>
        function openSettings() {
            chrome.runtime.openOptionsPage();
        }
        
        function openExtension() {
            chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                chrome.tabs.sendMessage(tabs[0].id, {action: "toggleInterface"});
                window.close();
            });
        }
        
        // Check API status
        async function checkAPIStatus() {
            try {
                const result = await chrome.storage.local.get(['chefai_llm_config']);
                const statusDot = document.getElementById('api-status');
                const statusText = document.getElementById('api-status-text');
                
                if (result.chefai_llm_config && result.chefai_llm_config.apiKey) {
                    statusDot.className = 'status-dot';
                    statusText.textContent = 'API Configured';
                } else {
                    statusDot.className = 'status-dot warning';
                    statusText.textContent = 'API Not Configured';
                }
            } catch (error) {
                const statusDot = document.getElementById('api-status');
                const statusText = document.getElementById('api-status-text');
                statusDot.className = 'status-dot error';
                statusText.textContent = 'API Error';
            }
        }
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', () => {
            checkAPIStatus();
        });
    </script>
</body>
</html>

// Sidebar Injector - Injects ChefAI sidebar into web pages
import { LLMService } from '../services/LLMService';
import { RecipeService } from '../services/RecipeService';

interface SidebarState {
  isVisible: boolean;
  isInitialized: boolean;
  container: HTMLElement | null;
}

class SidebarInjector {
  private state: SidebarState = {
    isVisible: false,
    isInitialized: false,
    container: null
  };

  private llmService: LLMService;
  private recipeService: RecipeService;
  private floatingButton: HTMLElement | null = null;

  constructor() {
    this.llmService = LLMService.getInstance();
    this.recipeService = RecipeService.getInstance();
    this.init();
  }

  private async init(): Promise<void> {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupSidebar());
    } else {
      this.setupSidebar();
    }

    // Listen for messages from extension
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'toggleSidebar') {
        this.toggleSidebar();
        sendResponse({ success: true });
      } else if (message.action === 'showSidebar') {
        this.showSidebar();
        sendResponse({ success: true });
      } else if (message.action === 'hideSidebar') {
        this.hideSidebar();
        sendResponse({ success: true });
      }
    });
  }

  private setupSidebar(): void {
    if (this.state.isInitialized) return;

    // Create floating button
    this.createFloatingButton();

    // Create sidebar container
    this.createSidebarContainer();

    // Load CSS
    this.loadSidebarStyles();

    this.state.isInitialized = true;
  }

  private createFloatingButton(): void {
    this.floatingButton = document.createElement('div');
    this.floatingButton.id = 'chefai-floating-button';
    this.floatingButton.innerHTML = '🍳';
    this.floatingButton.title = 'Open ChefAI Recipe Assistant';

    // Apply styles for left-side positioning
    Object.assign(this.floatingButton.style, {
      position: 'fixed',
      top: '20px',
      left: '20px',
      width: '60px',
      height: '60px',
      borderRadius: '50%',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      color: 'white',
      fontSize: '24px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      zIndex: '9999',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      border: '2px solid rgba(255, 255, 255, 0.2)',
      backdropFilter: 'blur(10px)',
      userSelect: 'none'
    });

    // Add hover effects
    this.floatingButton.addEventListener('mouseenter', () => {
      if (this.floatingButton) {
        this.floatingButton.style.transform = 'scale(1.1)';
        this.floatingButton.style.boxShadow = '0 6px 25px rgba(0, 0, 0, 0.4)';
      }
    });

    this.floatingButton.addEventListener('mouseleave', () => {
      if (this.floatingButton) {
        this.floatingButton.style.transform = 'scale(1)';
        this.floatingButton.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.3)';
      }
    });

    // Add click handler
    this.floatingButton.addEventListener('click', () => {
      this.toggleSidebar();
    });

    document.body.appendChild(this.floatingButton);
  }

  private createSidebarContainer(): void {
    this.state.container = document.createElement('div');
    this.state.container.id = 'chefai-sidebar-container';
    this.state.container.style.display = 'none';

    document.body.appendChild(this.state.container);
  }

  private loadSidebarStyles(): void {
    // Check if styles are already loaded
    if (document.getElementById('chefai-sidebar-styles')) return;

    const link = document.createElement('link');
    link.id = 'chefai-sidebar-styles';
    link.rel = 'stylesheet';
    link.href = chrome.runtime.getURL('src/styles/sidebar.css');
    document.head.appendChild(link);
  }

  private toggleSidebar(): void {
    if (this.state.isVisible) {
      this.hideSidebar();
    } else {
      this.showSidebar();
    }
  }

  private showSidebar(): void {
    if (!this.state.container) return;

    this.state.isVisible = true;
    this.state.container.style.display = 'block';

    // Render the sidebar interface
    this.renderSidebarInterface();

    // Trigger animation after a brief delay to ensure DOM is ready
    setTimeout(() => {
      const sidebarContent = this.state.container?.querySelector('.sidebar-content') as HTMLElement;
      if (sidebarContent) {
        sidebarContent.classList.add('visible');
      }
    }, 10);

    // Hide floating button when sidebar is open
    if (this.floatingButton) {
      this.floatingButton.style.display = 'none';
    }
  }

  private hideSidebar(): void {
    if (!this.state.container) return;

    // Animate out first
    const sidebarContent = this.state.container.querySelector('.sidebar-content') as HTMLElement;
    if (sidebarContent) {
      sidebarContent.classList.remove('visible');
    }

    // Hide after animation completes
    setTimeout(() => {
      if (this.state.container) {
        this.state.isVisible = false;
        this.state.container.style.display = 'none';
        this.state.container.innerHTML = '';
      }
    }, 300);

    // Show floating button when sidebar is closed
    if (this.floatingButton) {
      this.floatingButton.style.display = 'flex';
    }
  }

  private renderSidebarInterface(): void {
    if (!this.state.container) return;

    // Create the sidebar HTML structure
    this.state.container.innerHTML = `
      <div class="sidebar-interface">
        <div class="sidebar-overlay"></div>
        <div class="sidebar-content">
          <div class="sidebar-header">
            <h2>🍳 ChefAI Assistant</h2>
            <button class="close-btn" id="chefai-close-btn">×</button>
          </div>

          <div class="sidebar-tabs">
            <button class="tab active" data-tab="generate">
              <span class="tab-icon">✨</span>
              Generate Recipe
            </button>
            <button class="tab" data-tab="settings">
              <span class="tab-icon">⚙️</span>
              LLM Settings
            </button>
            <button class="tab" data-tab="history">
              <span class="tab-icon">📚</span>
              Recent Recipes
            </button>
          </div>

          <div class="tab-content" id="chefai-tab-content">
            <!-- Tab content will be rendered here -->
          </div>
        </div>
      </div>
    `;

    // Set up event listeners
    this.setupSidebarEventListeners();

    // Render initial tab content
    this.renderTabContent('generate');
  }

  private setupSidebarEventListeners(): void {
    if (!this.state.container) return;

    // Close button
    const closeBtn = this.state.container.querySelector('#chefai-close-btn');
    closeBtn?.addEventListener('click', () => this.hideSidebar());

    // Overlay click to close
    const overlay = this.state.container.querySelector('.sidebar-overlay');
    overlay?.addEventListener('click', () => this.hideSidebar());

    // Tab switching
    const tabs = this.state.container.querySelectorAll('.tab');
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        const tabName = tab.getAttribute('data-tab');
        if (tabName) {
          // Update active tab
          tabs.forEach(t => t.classList.remove('active'));
          tab.classList.add('active');

          // Render tab content
          this.renderTabContent(tabName);
        }
      });
    });
  }

  private renderTabContent(tabName: string): void {
    const contentContainer = this.state.container?.querySelector('#chefai-tab-content');
    if (!contentContainer) return;

    switch (tabName) {
      case 'generate':
        this.renderGenerateTab(contentContainer);
        break;
      case 'settings':
        this.renderSettingsTab(contentContainer);
        break;
      case 'history':
        this.renderHistoryTab(contentContainer);
        break;
    }
  }

  private renderGenerateTab(container: Element): void {
    container.innerHTML = `
      <div class="generate-tab">
        <div class="form-section">
          <h3>🥘 Recipe Requirements</h3>

          <div class="input-group">
            <label>Available Ingredients</label>
            <div class="ingredient-input">
              <input type="text" id="ingredient-input" placeholder="Enter an ingredient..." class="glass-input">
              <button id="add-ingredient-btn" class="add-btn glass-button">Add</button>
            </div>
            <div id="ingredients-list" class="ingredients-list"></div>
          </div>

          <div class="parameters-grid">
            <div class="input-group">
              <label>Cuisine Type</label>
              <select id="cuisine-select" class="glass-select">
                <option value="">Any Cuisine</option>
                <option value="Italian">Italian</option>
                <option value="Asian">Asian</option>
                <option value="Mexican">Mexican</option>
                <option value="Mediterranean">Mediterranean</option>
                <option value="Indian">Indian</option>
                <option value="French">French</option>
                <option value="American">American</option>
                <option value="Thai">Thai</option>
                <option value="Japanese">Japanese</option>
                <option value="Middle Eastern">Middle Eastern</option>
              </select>
            </div>

            <div class="input-group">
              <label>Meal Type</label>
              <select id="meal-type-select" class="glass-select">
                <option value="breakfast">Breakfast</option>
                <option value="lunch">Lunch</option>
                <option value="dinner" selected>Dinner</option>
                <option value="snack">Snack</option>
                <option value="dessert">Dessert</option>
                <option value="appetizer">Appetizer</option>
              </select>
            </div>

            <div class="input-group">
              <label>Servings</label>
              <input type="number" id="servings-input" min="1" max="12" value="4" class="glass-input">
            </div>

            <div class="input-group">
              <label>Max Cooking Time (minutes)</label>
              <input type="number" id="cook-time-input" min="5" max="240" value="30" class="glass-input">
            </div>

            <div class="input-group">
              <label>Difficulty</label>
              <select id="difficulty-select" class="glass-select">
                <option value="Easy">Easy</option>
                <option value="Medium" selected>Medium</option>
                <option value="Hard">Hard</option>
              </select>
            </div>
          </div>

          <div class="input-group">
            <label>Dietary Restrictions</label>
            <div class="dietary-options">
              <label class="checkbox-label">
                <input type="checkbox" value="Vegetarian"> Vegetarian
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Vegan"> Vegan
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Gluten-Free"> Gluten-Free
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Dairy-Free"> Dairy-Free
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Keto"> Keto
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Low-Carb"> Low-Carb
              </label>
              <label class="checkbox-label">
                <input type="checkbox" value="Paleo"> Paleo
              </label>
            </div>
          </div>

          <div class="input-group">
            <label>Additional Requirements (Optional)</label>
            <textarea id="custom-prompt" placeholder="Any specific requirements, cooking methods, or preferences..." class="glass-textarea" rows="3"></textarea>
          </div>

          <button id="generate-recipe-btn" class="generate-btn glass-button primary">
            <span class="btn-icon">✨</span>
            Generate Recipe with AI
          </button>

          <div id="generation-status" class="warning-message" style="display: none;"></div>
        </div>
      </div>
    `;

    this.setupGenerateTabListeners();
  }

  private setupGenerateTabListeners(): void {
    const ingredients: string[] = [];

    // Add ingredient functionality
    const ingredientInput = document.getElementById('ingredient-input') as HTMLInputElement;
    const addBtn = document.getElementById('add-ingredient-btn');
    const ingredientsList = document.getElementById('ingredients-list');

    const addIngredient = () => {
      const ingredient = ingredientInput?.value.trim();
      if (ingredient && !ingredients.includes(ingredient)) {
        ingredients.push(ingredient);
        this.updateIngredientsDisplay(ingredients, ingredientsList);
        if (ingredientInput) ingredientInput.value = '';
      }
    };

    addBtn?.addEventListener('click', addIngredient);
    ingredientInput?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') addIngredient();
    });

    // Generate recipe functionality
    const generateBtn = document.getElementById('generate-recipe-btn');
    generateBtn?.addEventListener('click', () => this.handleGenerateRecipe(ingredients));
  }

  private updateIngredientsDisplay(ingredients: string[], container: Element | null): void {
    if (!container) return;

    container.innerHTML = ingredients.map(ingredient => `
      <span class="ingredient-tag">
        ${ingredient}
        <button class="remove-ingredient" data-ingredient="${ingredient}">×</button>
      </span>
    `).join('');

    // Add remove functionality
    container.querySelectorAll('.remove-ingredient').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const ingredient = (e.target as HTMLElement).getAttribute('data-ingredient');
        if (ingredient) {
          const index = ingredients.indexOf(ingredient);
          if (index > -1) {
            ingredients.splice(index, 1);
            this.updateIngredientsDisplay(ingredients, container);
          }
        }
      });
    });
  }

  private async handleGenerateRecipe(ingredients: string[]): Promise<void> {
    const generateBtn = document.getElementById('generate-recipe-btn');
    const statusDiv = document.getElementById('generation-status');

    if (!generateBtn || !statusDiv) return;

    // Check if LLM is configured
    const config = this.llmService.getConfiguration();
    if (!config || !config.apiKey) {
      statusDiv.textContent = '⚠️ Please configure your LLM API key in settings first.';
      statusDiv.style.display = 'block';
      return;
    }

    // Disable button and show loading
    generateBtn.innerHTML = '<span class="spinner"></span> Generating Recipe...';
    (generateBtn as HTMLButtonElement).disabled = true;
    statusDiv.style.display = 'none';

    try {
      // Collect form data
      const request = this.collectGenerationRequest(ingredients);

      // Generate recipe
      const response = await this.llmService.generateRecipe(request);

      if (response.success && response.data) {
        // Save recipe
        await this.recipeService.saveRecipe(response.data);

        // Show success and close sidebar
        statusDiv.textContent = '✅ Recipe generated successfully! Check your recipes.';
        statusDiv.style.display = 'block';
        statusDiv.style.background = 'rgba(76, 175, 80, 0.2)';
        statusDiv.style.borderColor = 'rgba(76, 175, 80, 0.5)';

        // Auto-close after 2 seconds
        setTimeout(() => this.hideSidebar(), 2000);
      } else {
        statusDiv.textContent = `❌ Failed to generate recipe: ${response.error}`;
        statusDiv.style.display = 'block';
      }
    } catch (error) {
      statusDiv.textContent = '❌ An error occurred while generating the recipe.';
      statusDiv.style.display = 'block';
    } finally {
      // Re-enable button
      generateBtn.innerHTML = '<span class="btn-icon">✨</span> Generate Recipe with AI';
      (generateBtn as HTMLButtonElement).disabled = false;
    }
  }

  private collectGenerationRequest(ingredients: string[]): any {
    const cuisineSelect = document.getElementById('cuisine-select') as HTMLSelectElement;
    const mealTypeSelect = document.getElementById('meal-type-select') as HTMLSelectElement;
    const servingsInput = document.getElementById('servings-input') as HTMLInputElement;
    const cookTimeInput = document.getElementById('cook-time-input') as HTMLInputElement;
    const difficultySelect = document.getElementById('difficulty-select') as HTMLSelectElement;
    const customPrompt = document.getElementById('custom-prompt') as HTMLTextAreaElement;

    // Collect dietary restrictions
    const dietaryCheckboxes = document.querySelectorAll('.dietary-options input[type="checkbox"]:checked');
    const dietaryRestrictions = Array.from(dietaryCheckboxes).map(cb => (cb as HTMLInputElement).value);

    return {
      ingredients,
      cuisineType: cuisineSelect?.value || undefined,
      mealType: mealTypeSelect?.value || 'dinner',
      servings: parseInt(servingsInput?.value || '4'),
      cookingTime: parseInt(cookTimeInput?.value || '30'),
      difficulty: difficultySelect?.value || 'Medium',
      dietaryRestrictions: dietaryRestrictions.length > 0 ? dietaryRestrictions : undefined,
      customPrompt: customPrompt?.value || undefined
    };
  }

  private renderSettingsTab(container: Element): void {
    container.innerHTML = `
      <div class="settings-tab">
        <div class="llm-settings">
          <h3>🤖 LLM Configuration</h3>
          <div id="settings-content">Loading settings...</div>
        </div>
      </div>
    `;

    this.loadLLMSettings();
  }

  private async loadLLMSettings(): Promise<void> {
    const settingsContent = document.getElementById('settings-content');
    if (!settingsContent) return;

    const config = this.llmService.getConfiguration();
    const providers = this.llmService.getProviders();

    settingsContent.innerHTML = `
      <div class="settings-form">
        <div class="input-group">
          <label>AI Provider</label>
          <select id="provider-select" class="glass-select">
            ${providers.map(p => `
              <option value="${p.id}" ${config?.provider === p.id ? 'selected' : ''}>
                ${p.name}
              </option>
            `).join('')}
          </select>
        </div>

        <div class="input-group">
          <label>API Key</label>
          <input type="password" id="api-key-input" value="${config?.apiKey || ''}" placeholder="Enter your API key..." class="glass-input">
          <small>Get your API key from the provider's website</small>
        </div>

        <div class="input-group">
          <label>Temperature (${config?.temperature || 0.7})</label>
          <input type="range" id="temperature-range" min="0" max="2" step="0.1" value="${config?.temperature || 0.7}" class="glass-range">
          <small>Higher = more creative, Lower = more focused</small>
        </div>

        <div class="settings-actions">
          <button id="test-connection-btn" class="test-btn glass-button">Test Connection</button>
          <button id="save-config-btn" class="save-btn glass-button primary">Save Configuration</button>
        </div>

        <div id="test-result" class="test-result" style="display: none;"></div>
      </div>
    `;

    this.setupSettingsListeners();
  }

  private setupSettingsListeners(): void {
    const saveBtn = document.getElementById('save-config-btn');
    const testBtn = document.getElementById('test-connection-btn');

    saveBtn?.addEventListener('click', () => this.saveConfiguration());
    testBtn?.addEventListener('click', () => this.testConnection());
  }

  private async saveConfiguration(): Promise<void> {
    const providerSelect = document.getElementById('provider-select') as HTMLSelectElement;
    const apiKeyInput = document.getElementById('api-key-input') as HTMLInputElement;
    const temperatureRange = document.getElementById('temperature-range') as HTMLInputElement;

    const config = {
      provider: providerSelect?.value || 'openrouter',
      model: 'anthropic/claude-3.5-sonnet', // Default model
      apiKey: apiKeyInput?.value || '',
      temperature: parseFloat(temperatureRange?.value || '0.7'),
      maxTokens: 2048,
      systemPrompt: 'You are ChefAI, an expert culinary assistant. Generate detailed, practical recipes with clear instructions, ingredient lists, and cooking tips.'
    };

    try {
      await this.llmService.saveConfiguration(config);
      alert('Configuration saved successfully!');
    } catch (error) {
      alert('Failed to save configuration.');
    }
  }

  private async testConnection(): Promise<void> {
    const testBtn = document.getElementById('test-connection-btn');
    const testResult = document.getElementById('test-result');

    if (!testBtn || !testResult) return;

    testBtn.textContent = 'Testing...';
    (testBtn as HTMLButtonElement).disabled = true;
    testResult.style.display = 'none';

    try {
      const result = await this.llmService.testConnection();
      testResult.textContent = result.success ? '✅ Connection successful!' : `❌ ${result.error}`;
      testResult.className = `test-result ${result.success ? 'success' : 'error'}`;
      testResult.style.display = 'block';
    } catch (error) {
      testResult.textContent = `❌ Connection failed: ${error.message}`;
      testResult.className = 'test-result error';
      testResult.style.display = 'block';
    } finally {
      testBtn.textContent = 'Test Connection';
      (testBtn as HTMLButtonElement).disabled = false;
    }
  }

  private async renderHistoryTab(container: Element): Promise<void> {
    container.innerHTML = `
      <div class="history-tab">
        <h3>📚 Recent AI Recipes</h3>
        <div id="recipes-list">Loading recipes...</div>
      </div>
    `;

    try {
      const recipes = await this.recipeService.getRecipes();
      const aiRecipes = recipes.filter(r => r.source === 'AI Generated').slice(0, 10);

      const recipesList = document.getElementById('recipes-list');
      if (!recipesList) return;

      if (aiRecipes.length === 0) {
        recipesList.innerHTML = `
          <div class="empty-state">
            <p>No AI-generated recipes yet.</p>
            <p>Generate your first recipe to see it here!</p>
          </div>
        `;
      } else {
        recipesList.innerHTML = aiRecipes.map(recipe => `
          <div class="recipe-item glass-card">
            <h4>${recipe.title}</h4>
            <p>${recipe.description}</p>
            <div class="recipe-meta">
              <span>🍽️ ${recipe.servings} servings</span>
              <span>⏱️ ${recipe.cookTime} min</span>
              <span>📊 ${recipe.difficulty}</span>
            </div>
          </div>
        `).join('');
      }
    } catch (error) {
      const recipesList = document.getElementById('recipes-list');
      if (recipesList) {
        recipesList.innerHTML = '<div class="empty-state"><p>Failed to load recipes.</p></div>';
      }
    }
  }
}

// Initialize the sidebar injector
new SidebarInjector();
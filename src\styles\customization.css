/* Customization Panel Styles */
.customization-panel {
  max-width: 900px;
  margin: 20px auto;
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 35px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
  backdrop-filter: blur(25px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.panel-title {
  margin: 0;
  font-size: 2em;
  font-weight: 700;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.panel-close {
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: white;
  font-size: 1.8em;
  cursor: pointer;
  padding: 0;
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
}

.panel-close:hover {
  background: rgba(255, 100, 100, 0.3);
  border-color: rgba(255, 100, 100, 0.5);
  transform: rotate(90deg) scale(1.05);
}

.panel-navigation {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.nav-section {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px 25px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.nav-section:last-child {
  border-right: none;
}

.nav-section::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.nav-section.active {
  color: white;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 165, 0, 0.1));
}

.nav-section.active::before {
  transform: scaleX(1);
}

.nav-section:hover:not(.active) {
  color: rgba(255, 255, 255, 0.95);
  background: rgba(255, 255, 255, 0.08);
}

.section-icon {
  font-size: 1.8em;
  min-width: 40px;
  text-align: center;
}

.section-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.section-name {
  font-size: 1.1em;
  font-weight: 600;
}

.section-description {
  font-size: 0.85em;
  opacity: 0.8;
  line-height: 1.3;
}

.panel-content {
  padding: 35px;
  max-height: 70vh;
  overflow-y: auto;
}

.settings-section h3 {
  margin: 0 0 30px 0;
  font-size: 1.6em;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.setting-group {
  margin-bottom: 35px;
  padding: 25px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.setting-group h4 {
  margin: 0 0 20px 0;
  font-size: 1.2em;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 10px;
}

.setting-item {
  margin-bottom: 20px;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 1em;
  color: rgba(255, 255, 255, 0.95);
}

.glass-input,
.glass-select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.12);
  color: white;
  font-size: 14px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.glass-input:focus,
.glass-select:focus {
  outline: none;
  border-color: rgba(255, 215, 0, 0.6);
  background: rgba(255, 255, 255, 0.18);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
}

.glass-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.glass-select option {
  background: #333;
  color: white;
}

.glass-slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  -webkit-appearance: none;
  margin: 10px 0;
}

.glass-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  cursor: pointer;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.glass-slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  cursor: pointer;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.85em;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 5px;
}

.input-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.input-group {
  display: flex;
  flex-direction: column;
}

.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-top: 15px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.15);
  font-size: 0.95em;
}

.checkbox-label:hover {
  background: rgba(255, 255, 255, 0.12);
  transform: translateY(-1px);
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #FFD700;
  margin: 0;
}

.panel-actions {
  display: flex;
  gap: 15px;
  padding: 25px 35px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  flex-wrap: wrap;
}

.glass-button {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  backdrop-filter: blur(10px);
  font-size: 0.95em;
}

.glass-button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.glass-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.glass-button.primary {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  border-color: #4CAF50;
}

.glass-button.primary:hover:not(:disabled) {
  background: linear-gradient(45deg, #45a049, #3d8b40);
}

.glass-button.danger {
  background: linear-gradient(45deg, #f44336, #d32f2f);
  border-color: #f44336;
}

.glass-button.danger:hover:not(:disabled) {
  background: linear-gradient(45deg, #d32f2f, #b71c1c);
}

.import-button {
  position: relative;
  overflow: hidden;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  gap: 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .customization-panel {
    margin: 10px;
    border-radius: 15px;
  }
  
  .panel-header {
    padding: 20px 25px;
  }
  
  .panel-title {
    font-size: 1.6em;
  }
  
  .panel-navigation {
    grid-template-columns: 1fr;
  }
  
  .nav-section {
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .nav-section:last-child {
    border-bottom: none;
  }
  
  .panel-content {
    padding: 25px;
  }
  
  .checkbox-grid {
    grid-template-columns: 1fr;
  }
  
  .input-row {
    grid-template-columns: 1fr;
  }
  
  .panel-actions {
    flex-direction: column;
    padding: 20px 25px;
  }
}

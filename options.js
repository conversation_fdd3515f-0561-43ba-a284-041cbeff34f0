// ChefAI Options Page Script
document.addEventListener('DOMContentLoaded', function() {
  console.log('ChefAI Options page loaded');
  
  // Initialize options page
  initializeOptionsPage();
});

function initializeOptionsPage() {
  // Load saved settings
  loadSettings();
  
  // Set up event listeners
  setupEventListeners();
  
  // Setup tab navigation
  setupTabNavigation();
}

function setupTabNavigation() {
  const navItems = document.querySelectorAll('.nav-item');
  const tabContents = document.querySelectorAll('.tab-content');

  navItems.forEach(item => {
    item.addEventListener('click', () => {
      const tabId = item.getAttribute('data-tab');
      
      // Remove active class from all nav items and tab contents
      navItems.forEach(nav => nav.classList.remove('active'));
      tabContents.forEach(tab => tab.classList.remove('active'));
      
      // Add active class to clicked nav item and corresponding tab
      item.classList.add('active');
      const targetTab = document.getElementById(`${tabId}-tab`);
      if (targetTab) {
        targetTab.classList.add('active');
      }
    });
  });
}

function setupEventListeners() {
  // API Key input
  const apiKeyInput = document.getElementById('gemini-api-key');
  if (apiKeyInput) {
    apiKeyInput.addEventListener('input', saveApiKey);
  }

  // Test connection button
  const testConnectionBtn = document.getElementById('test-connection-btn');
  if (testConnectionBtn) {
    testConnectionBtn.addEventListener('click', testGeminiConnection);
  }

  // Setup sliders for all model sections
  setupModelSliders('planner');
  setupModelSliders('navigator');
  setupModelSliders('validator');

  // Model selections
  const modelSelects = [
    'planner-model-select',
    'navigator-model-select',
    'validator-model-select',
    'speech-model-select'
  ];

  modelSelects.forEach(selectId => {
    const modelSelect = document.getElementById(selectId);
    if (modelSelect) {
      modelSelect.addEventListener('change', saveModelSettings);
    }
  });

  // Add provider button and dropdown
  const addProviderBtn = document.getElementById('add-provider-btn');
  const providerDropdown = document.getElementById('provider-dropdown');

  if (addProviderBtn && providerDropdown) {
    addProviderBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      providerDropdown.style.display = providerDropdown.style.display === 'none' ? 'block' : 'none';
    });

    // Provider selection
    const providerOptions = document.querySelectorAll('.provider-option');
    providerOptions.forEach(option => {
      option.addEventListener('click', (e) => {
        const provider = e.target.getAttribute('data-provider');
        addProviderCard(provider);
        providerDropdown.style.display = 'none';
      });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!addProviderBtn.contains(e.target) && !providerDropdown.contains(e.target)) {
        providerDropdown.style.display = 'none';
      }
    });
  }

  // Delete buttons
  const deleteButtons = document.querySelectorAll('.delete-btn');
  deleteButtons.forEach(btn => {
    btn.addEventListener('click', (e) => {
      const providerCard = e.target.closest('.provider-card');
      if (providerCard && confirm('Are you sure you want to delete this provider?')) {
        providerCard.remove();
        saveSettings();
      }
    });
  });

  // Model tag remove buttons
  const modelTagButtons = document.querySelectorAll('.model-tag button');
  modelTagButtons.forEach(btn => {
    btn.addEventListener('click', (e) => {
      const modelTag = e.target.closest('.model-tag');
      if (modelTag) {
        modelTag.remove();
        saveSettings();
      }
    });
  });

  // General settings inputs
  const settingInputs = document.querySelectorAll('.setting-input');
  settingInputs.forEach(input => {
    input.addEventListener('change', saveGeneralSettings);
  });

  // Toggle switches
  const toggleSwitches = document.querySelectorAll('.toggle-switch input');
  toggleSwitches.forEach(toggle => {
    toggle.addEventListener('change', saveGeneralSettings);
  });

  // Firewall tabs
  const firewallTabs = document.querySelectorAll('.firewall-tab-btn');
  firewallTabs.forEach(tab => {
    tab.addEventListener('click', (e) => {
      firewallTabs.forEach(t => t.classList.remove('active'));
      e.target.classList.add('active');
      updateFirewallView(e.target.getAttribute('data-tab'));
    });
  });

  // Firewall add button
  const firewallAddBtn = document.querySelector('.firewall-add-btn');
  const firewallInput = document.querySelector('.firewall-input');
  if (firewallAddBtn && firewallInput) {
    firewallAddBtn.addEventListener('click', () => {
      const domain = firewallInput.value.trim();
      if (domain) {
        addFirewallRule(domain);
        firewallInput.value = '';
      }
    });

    firewallInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        const domain = firewallInput.value.trim();
        if (domain) {
          addFirewallRule(domain);
          firewallInput.value = '';
        }
      }
    });
  }
}

async function loadSettings() {
  try {
    const result = await chrome.storage.local.get([
      'chefai_llm_config',
      'chefai_advanced_settings',
      'chefai_general_settings',
      'chefai_model_settings',
      'chefai_firewall_allow',
      'chefai_firewall_deny'
    ]);

    // Load LLM configuration
    if (result.chefai_llm_config) {
      const config = result.chefai_llm_config;

      // Load API key (show masked)
      const apiKeyInput = document.getElementById('gemini-api-key');
      if (apiKeyInput && config.apiKey) {
        apiKeyInput.value = '••••••••••••••••••••••••••••••';
        apiKeyInput.dataset.realValue = config.apiKey;
      }

      // Load model selection
      const modelSelect = document.getElementById('model-select');
      if (modelSelect && config.model) {
        modelSelect.value = config.model;
      }
    }

    // Load advanced settings (sliders)
    if (result.chefai_advanced_settings) {
      const settings = result.chefai_advanced_settings;

      // Load temperature
      const temperatureSlider = document.getElementById('temperature-slider');
      const temperatureValue = document.getElementById('temperature-value');
      if (temperatureSlider && settings.temperature !== undefined) {
        temperatureSlider.value = settings.temperature;
        if (temperatureValue) {
          temperatureValue.textContent = parseFloat(settings.temperature).toFixed(2);
        }
      }

      // Load top P
      const topPSlider = document.getElementById('top-p-slider');
      const topPValue = document.getElementById('top-p-value');
      if (topPSlider && settings.topP !== undefined) {
        topPSlider.value = settings.topP;
        if (topPValue) {
          topPValue.textContent = parseFloat(settings.topP).toFixed(3);
        }
      }
    }

    // Load general settings
    if (result.chefai_general_settings) {
      const settings = result.chefai_general_settings;
      const settingInputs = document.querySelectorAll('.setting-input');
      const toggleSwitches = document.querySelectorAll('.toggle-switch input');

      // Load numeric settings
      if (settingInputs[0]) settingInputs[0].value = settings.maxStepsPerTask || 100;
      if (settingInputs[1]) settingInputs[1].value = settings.maxActionsPerStep || 5;
      if (settingInputs[2]) settingInputs[2].value = settings.failureTolerance || 3;
      if (settingInputs[3]) settingInputs[3].value = settings.replanningFrequency || 3;
      if (settingInputs[4]) settingInputs[4].value = settings.pageLoadWaitTime || 250;

      // Load toggle switches
      if (toggleSwitches[0]) toggleSwitches[0].checked = settings.enableVision !== false;
      if (toggleSwitches[1]) toggleSwitches[1].checked = settings.displayHighlights !== false;
    }

    // Load model settings
    if (result.chefai_model_settings) {
      const settings = result.chefai_model_settings;

      // Load planner settings
      if (settings.planner) {
        const plannerModelSelect = document.getElementById('planner-model-select');
        const plannerTempSlider = document.getElementById('planner-temperature-slider');
        const plannerTempValue = document.getElementById('planner-temperature-value');
        const plannerTopPSlider = document.getElementById('planner-top-p-slider');
        const plannerTopPValue = document.getElementById('planner-top-p-value');

        if (plannerModelSelect) plannerModelSelect.value = settings.planner.model;
        if (plannerTempSlider) plannerTempSlider.value = settings.planner.temperature;
        if (plannerTempValue) plannerTempValue.textContent = settings.planner.temperature.toFixed(2);
        if (plannerTopPSlider) plannerTopPSlider.value = settings.planner.topP;
        if (plannerTopPValue) plannerTopPValue.textContent = settings.planner.topP.toFixed(3);
      }

      // Load navigator settings
      if (settings.navigator) {
        const navigatorModelSelect = document.getElementById('navigator-model-select');
        const navigatorTempSlider = document.getElementById('navigator-temperature-slider');
        const navigatorTempValue = document.getElementById('navigator-temperature-value');
        const navigatorTopPSlider = document.getElementById('navigator-top-p-slider');
        const navigatorTopPValue = document.getElementById('navigator-top-p-value');

        if (navigatorModelSelect) navigatorModelSelect.value = settings.navigator.model;
        if (navigatorTempSlider) navigatorTempSlider.value = settings.navigator.temperature;
        if (navigatorTempValue) navigatorTempValue.textContent = settings.navigator.temperature.toFixed(2);
        if (navigatorTopPSlider) navigatorTopPSlider.value = settings.navigator.topP;
        if (navigatorTopPValue) navigatorTopPValue.textContent = settings.navigator.topP.toFixed(3);
      }

      // Load validator settings
      if (settings.validator) {
        const validatorModelSelect = document.getElementById('validator-model-select');
        const validatorTempSlider = document.getElementById('validator-temperature-slider');
        const validatorTempValue = document.getElementById('validator-temperature-value');
        const validatorTopPSlider = document.getElementById('validator-top-p-slider');
        const validatorTopPValue = document.getElementById('validator-top-p-value');

        if (validatorModelSelect) validatorModelSelect.value = settings.validator.model;
        if (validatorTempSlider) validatorTempSlider.value = settings.validator.temperature;
        if (validatorTempValue) validatorTempValue.textContent = settings.validator.temperature.toFixed(2);
        if (validatorTopPSlider) validatorTopPSlider.value = settings.validator.topP;
        if (validatorTopPValue) validatorTopPValue.textContent = settings.validator.topP.toFixed(3);
      }

      // Load speech-to-text settings
      if (settings.speechToText) {
        const speechModelSelect = document.getElementById('speech-model-select');
        if (speechModelSelect) speechModelSelect.value = settings.speechToText.model;
      }
    }

    // Load firewall settings
    const enableFirewallToggle = document.getElementById('enable-firewall');
    if (enableFirewallToggle) {
      enableFirewallToggle.checked = true; // Default enabled
    }

    // Load firewall rules
    if (result.chefai_firewall_allow) {
      updateFirewallRulesList('allow', result.chefai_firewall_allow);
    }

    if (result.chefai_firewall_deny) {
      updateFirewallRulesList('deny', result.chefai_firewall_deny);
    }

  } catch (error) {
    console.error('Error loading settings:', error);
  }
}

async function saveSettings() {
  try {
    const temperatureSlider = document.querySelector('input[type="range"][min="0"][max="1"]');
    const topPSlider = document.querySelectorAll('input[type="range"][min="0"][max="1"]')[1];
    const modelSelect = document.querySelector('.form-select');

    const settings = {
      temperature: temperatureSlider ? parseFloat(temperatureSlider.value) : 0.7,
      topP: topPSlider ? parseFloat(topPSlider.value) : 0.9,
      model: modelSelect ? modelSelect.value : 'gemini-2.5-flash-preview-05-20'
    };

    await chrome.storage.local.set({
      chefai_advanced_settings: settings
    });

    console.log('Settings saved:', settings);

  } catch (error) {
    console.error('Error saving settings:', error);
  }
}

async function saveApiKey() {
  try {
    const apiKeyInput = document.getElementById('gemini-api-key');
    if (!apiKeyInput) return;

    let apiKey = apiKeyInput.value;
    
    // If it's the masked value, use the real value
    if (apiKey === '••••••••••••••••••••••••••••••' && apiKeyInput.dataset.realValue) {
      apiKey = apiKeyInput.dataset.realValue;
    }

    const config = {
      provider: 'gemini',
      apiKey: apiKey,
      model: 'gemini-2.5-flash-preview-05-20'
    };

    await chrome.storage.local.set({
      chefai_llm_config: config
    });

    console.log('API key saved');

  } catch (error) {
    console.error('Error saving API key:', error);
  }
}

function showAddProviderDialog() {
  const providers = ['OpenRouter', 'OpenAI', 'Anthropic'];
  const provider = prompt(`Add new provider. Choose from:\n${providers.join(', ')}`);
  
  if (provider && providers.includes(provider)) {
    addProviderCard(provider.toLowerCase());
  }
}

async function saveGeneralSettings() {
  try {
    const settings = {
      maxStepsPerTask: parseInt(document.querySelector('.setting-input[value="100"]')?.value || 100),
      maxActionsPerStep: parseInt(document.querySelector('.setting-input[value="5"]')?.value || 5),
      failureTolerance: parseInt(document.querySelector('.setting-input[value="3"]')?.value || 3),
      enableVision: document.querySelector('.toggle-switch input')?.checked || true,
      displayHighlights: document.querySelectorAll('.toggle-switch input')[1]?.checked || true,
      replanningFrequency: parseInt(document.querySelector('.setting-input[value="3"]')?.value || 3),
      pageLoadWaitTime: parseInt(document.querySelector('.setting-input[value="250"]')?.value || 250)
    };

    await chrome.storage.local.set({
      chefai_general_settings: settings
    });

    console.log('General settings saved:', settings);
  } catch (error) {
    console.error('Error saving general settings:', error);
  }
}

function updateFirewallView(tabType) {
  const statusElement = document.querySelector('.firewall-status p');
  if (statusElement) {
    if (tabType === 'allow') {
      statusElement.textContent = 'No domains in allow list. Empty allow list means all non-denied domains are allowed.';
    } else {
      statusElement.textContent = 'No domains in deny list. Empty deny list means no domains are blocked.';
    }
  }
}

async function addFirewallRule(domain) {
  try {
    const activeTab = document.querySelector('.firewall-tab-btn.active').getAttribute('data-tab');
    const storageKey = activeTab === 'allow' ? 'chefai_firewall_allow' : 'chefai_firewall_deny';

    const result = await chrome.storage.local.get([storageKey]);
    const rules = result[storageKey] || [];

    if (!rules.includes(domain)) {
      rules.push(domain);
      await chrome.storage.local.set({
        [storageKey]: rules
      });

      // Update UI to show the new rule
      updateFirewallRulesList(activeTab, rules);
    }
  } catch (error) {
    console.error('Error adding firewall rule:', error);
  }
}

function updateFirewallRulesList(tabType, rules) {
  const statusElement = document.querySelector('.firewall-status');
  if (rules.length === 0) {
    statusElement.innerHTML = `
      <p style="color: #7f8c8d; font-size: 14px; text-align: center; margin: 20px 0;">
        No domains in ${tabType} list. ${tabType === 'allow' ? 'Empty allow list means all non-denied domains are allowed.' : 'Empty deny list means no domains are blocked.'}
      </p>
    `;
  } else {
    const rulesList = rules.map(rule => `
      <div class="firewall-rule">
        <span>${rule}</span>
        <button class="remove-rule-btn" data-rule="${rule}">×</button>
      </div>
    `).join('');

    statusElement.innerHTML = `
      <div class="firewall-rules-list">
        ${rulesList}
      </div>
    `;

    // Add event listeners for remove buttons
    const removeButtons = statusElement.querySelectorAll('.remove-rule-btn');
    removeButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const rule = e.target.getAttribute('data-rule');
        removeFirewallRule(rule, tabType);
      });
    });
  }
}

async function removeFirewallRule(domain, tabType) {
  try {
    const storageKey = tabType === 'allow' ? 'chefai_firewall_allow' : 'chefai_firewall_deny';
    const result = await chrome.storage.local.get([storageKey]);
    const rules = result[storageKey] || [];

    const updatedRules = rules.filter(rule => rule !== domain);
    await chrome.storage.local.set({
      [storageKey]: updatedRules
    });

    updateFirewallRulesList(tabType, updatedRules);
  } catch (error) {
    console.error('Error removing firewall rule:', error);
  }
}

function addProviderCard(providerName) {
  const providersContainer = document.querySelector('.provider-card').parentNode;

  const providerCard = document.createElement('div');
  providerCard.className = 'provider-card';

  const displayName = providerName.charAt(0).toUpperCase() + providerName.slice(1);
  const models = getDefaultModelsForProvider(providerName);

  providerCard.innerHTML = `
    <div class="provider-header">
      <h3 class="provider-name">${displayName}</h3>
      <button class="delete-btn">Delete</button>
    </div>
    <div class="form-group">
      <label class="form-label">API Key*</label>
      <input type="password" class="form-input" placeholder="Enter your API key">
    </div>
    <div class="form-group">
      <label class="form-label">Models</label>
      <div class="model-tags">
        ${models.map(model => `
          <span class="model-tag">
            ${model}
            <button>×</button>
          </span>
        `).join('')}
      </div>
      <div style="margin-top: 12px; font-size: 12px; color: #7f8c8d;">
        Type and Press Enter or Space to add
      </div>
    </div>
  `;

  // Insert before the add provider button
  const addProviderBtn = document.getElementById('add-provider-btn');
  addProviderBtn.parentNode.insertBefore(providerCard, addProviderBtn);

  // Re-setup event listeners for the new card
  setupEventListeners();
}

function setupModelSliders(modelType) {
  // Temperature slider
  const temperatureSlider = document.getElementById(`${modelType}-temperature-slider`);
  const temperatureValue = document.getElementById(`${modelType}-temperature-value`);
  if (temperatureSlider && temperatureValue) {
    temperatureSlider.addEventListener('input', (e) => {
      const value = parseFloat(e.target.value).toFixed(2);
      temperatureValue.textContent = value;
      saveModelSettings();
    });
  }

  // Top-P slider
  const topPSlider = document.getElementById(`${modelType}-top-p-slider`);
  const topPValue = document.getElementById(`${modelType}-top-p-value`);
  if (topPSlider && topPValue) {
    topPSlider.addEventListener('input', (e) => {
      const value = parseFloat(e.target.value).toFixed(3);
      topPValue.textContent = value;
      saveModelSettings();
    });
  }
}

async function saveModelSettings() {
  try {
    const modelSettings = {
      planner: {
        model: document.getElementById('planner-model-select')?.value || 'gemini-2.5-flash-preview-05-20',
        temperature: parseFloat(document.getElementById('planner-temperature-slider')?.value || 0.30),
        topP: parseFloat(document.getElementById('planner-top-p-slider')?.value || 0.85)
      },
      navigator: {
        model: document.getElementById('navigator-model-select')?.value || 'gemini-2.5-flash-preview-05-20',
        temperature: parseFloat(document.getElementById('navigator-temperature-slider')?.value || 0.30),
        topP: parseFloat(document.getElementById('navigator-top-p-slider')?.value || 0.85)
      },
      validator: {
        model: document.getElementById('validator-model-select')?.value || 'gemini-2.5-flash-preview-05-20',
        temperature: parseFloat(document.getElementById('validator-temperature-slider')?.value || 0.10),
        topP: parseFloat(document.getElementById('validator-top-p-slider')?.value || 0.80)
      },
      speechToText: {
        model: document.getElementById('speech-model-select')?.value || 'gemini-2.5-flash-preview-05-20'
      }
    };

    await chrome.storage.local.set({
      chefai_model_settings: modelSettings
    });

    console.log('Model settings saved:', modelSettings);
  } catch (error) {
    console.error('Error saving model settings:', error);
  }
}

function getDefaultModelsForProvider(provider) {
  const defaultModels = {
    openai: ['gpt-4', 'gpt-3.5-turbo', 'gpt-4-turbo'],
    anthropic: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
    deepseek: ['deepseek-chat', 'deepseek-coder'],
    grok: ['grok-beta'],
    ollama: ['llama2', 'codellama', 'mistral'],
    azure: ['gpt-4', 'gpt-35-turbo'],
    openrouter: ['openai/gpt-4', 'anthropic/claude-3-opus', 'meta-llama/llama-2-70b-chat'],
    groq: ['llama2-70b-4096', 'mixtral-8x7b-32768'],
    cerebras: ['llama3.1-8b', 'llama3.1-70b'],
    'openai-compatible': ['custom-model-1']
  };

  return defaultModels[provider] || ['custom-model'];
}

async function testGeminiConnection() {
  const testBtn = document.getElementById('test-connection-btn');
  const statusDiv = document.getElementById('connection-status');

  if (!testBtn || !statusDiv) return;

  // Show loading state
  testBtn.textContent = 'Testing...';
  testBtn.disabled = true;
  statusDiv.style.display = 'block';
  statusDiv.style.background = '#f39c12';
  statusDiv.style.color = '#ffffff';
  statusDiv.textContent = 'Testing connection...';

  try {
    // Initialize Gemini API
    const geminiAPI = new GeminiAPI();
    const initialized = await geminiAPI.initialize();

    if (!initialized) {
      throw new Error('API key not configured. Please enter your Gemini API key.');
    }

    // Test the connection
    const result = await geminiAPI.testConnection();

    if (result.success) {
      statusDiv.style.background = '#27ae60';
      statusDiv.textContent = '✅ ' + result.message;
    } else {
      throw new Error(result.error);
    }

  } catch (error) {
    statusDiv.style.background = '#e74c3c';
    statusDiv.textContent = '❌ ' + error.message;
  } finally {
    // Reset button
    testBtn.textContent = 'Test Connection';
    testBtn.disabled = false;

    // Hide status after 5 seconds
    setTimeout(() => {
      statusDiv.style.display = 'none';
    }, 5000);
  }
}

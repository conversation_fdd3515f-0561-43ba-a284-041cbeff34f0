// ChefAI Options Page Script
document.addEventListener('DOMContentLoaded', function() {
  console.log('ChefAI Options page loaded');
  
  // Initialize options page
  initializeOptionsPage();
});

function initializeOptionsPage() {
  // Load saved settings
  loadSettings();
  
  // Set up event listeners
  setupEventListeners();
  
  // Setup tab navigation
  setupTabNavigation();
}

function setupTabNavigation() {
  const navItems = document.querySelectorAll('.nav-item');
  const tabContents = document.querySelectorAll('.tab-content');

  navItems.forEach(item => {
    item.addEventListener('click', () => {
      const tabId = item.getAttribute('data-tab');
      
      // Remove active class from all nav items and tab contents
      navItems.forEach(nav => nav.classList.remove('active'));
      tabContents.forEach(tab => tab.classList.remove('active'));
      
      // Add active class to clicked nav item and corresponding tab
      item.classList.add('active');
      const targetTab = document.getElementById(`${tabId}-tab`);
      if (targetTab) {
        targetTab.classList.add('active');
      }
    });
  });
}

function setupEventListeners() {
  // API Key input
  const apiKeyInput = document.getElementById('gemini-api-key');
  if (apiKeyInput) {
    apiKeyInput.addEventListener('input', saveApiKey);
  }

  // Sliders
  const temperatureSlider = document.querySelector('input[type="range"][min="0"][max="1"]');
  const topPSlider = document.querySelectorAll('input[type="range"][min="0"][max="1"]')[1];

  if (temperatureSlider) {
    temperatureSlider.addEventListener('input', (e) => {
      const value = parseFloat(e.target.value).toFixed(2);
      const valueDisplay = document.querySelector('.slider-value');
      if (valueDisplay) {
        valueDisplay.textContent = value;
      }
      saveSettings();
    });
  }

  if (topPSlider) {
    topPSlider.addEventListener('input', (e) => {
      const value = parseFloat(e.target.value).toFixed(3);
      const valueDisplays = document.querySelectorAll('.slider-value');
      if (valueDisplays[1]) {
        valueDisplays[1].textContent = value;
      }
      saveSettings();
    });
  }

  // Model selection
  const modelSelect = document.querySelector('.form-select');
  if (modelSelect) {
    modelSelect.addEventListener('change', saveSettings);
  }

  // Add provider button
  const addProviderBtn = document.querySelector('.add-provider-btn');
  if (addProviderBtn) {
    addProviderBtn.addEventListener('click', showAddProviderDialog);
  }

  // Delete buttons
  const deleteButtons = document.querySelectorAll('.delete-btn');
  deleteButtons.forEach(btn => {
    btn.addEventListener('click', (e) => {
      const providerCard = e.target.closest('.provider-card');
      if (providerCard && confirm('Are you sure you want to delete this provider?')) {
        providerCard.remove();
        saveSettings();
      }
    });
  });

  // Model tag remove buttons
  const modelTagButtons = document.querySelectorAll('.model-tag button');
  modelTagButtons.forEach(btn => {
    btn.addEventListener('click', (e) => {
      const modelTag = e.target.closest('.model-tag');
      if (modelTag) {
        modelTag.remove();
        saveSettings();
      }
    });
  });
}

async function loadSettings() {
  try {
    const result = await chrome.storage.local.get([
      'chefai_llm_config',
      'chefai_advanced_settings'
    ]);

    if (result.chefai_llm_config) {
      const config = result.chefai_llm_config;
      
      // Load API key (show masked)
      const apiKeyInput = document.getElementById('gemini-api-key');
      if (apiKeyInput && config.apiKey) {
        apiKeyInput.value = '••••••••••••••••••••••••••••••';
        apiKeyInput.dataset.realValue = config.apiKey;
      }

      // Load model selection
      const modelSelect = document.querySelector('.form-select');
      if (modelSelect && config.model) {
        modelSelect.value = config.model;
      }
    }

    if (result.chefai_advanced_settings) {
      const settings = result.chefai_advanced_settings;
      
      // Load temperature
      const temperatureSlider = document.querySelector('input[type="range"][min="0"][max="1"]');
      const temperatureValue = document.querySelector('.slider-value');
      if (temperatureSlider && settings.temperature !== undefined) {
        temperatureSlider.value = settings.temperature;
        if (temperatureValue) {
          temperatureValue.textContent = parseFloat(settings.temperature).toFixed(2);
        }
      }

      // Load top P
      const topPSlider = document.querySelectorAll('input[type="range"][min="0"][max="1"]')[1];
      const topPValue = document.querySelectorAll('.slider-value')[1];
      if (topPSlider && settings.topP !== undefined) {
        topPSlider.value = settings.topP;
        if (topPValue) {
          topPValue.textContent = parseFloat(settings.topP).toFixed(3);
        }
      }
    }

  } catch (error) {
    console.error('Error loading settings:', error);
  }
}

async function saveSettings() {
  try {
    const temperatureSlider = document.querySelector('input[type="range"][min="0"][max="1"]');
    const topPSlider = document.querySelectorAll('input[type="range"][min="0"][max="1"]')[1];
    const modelSelect = document.querySelector('.form-select');

    const settings = {
      temperature: temperatureSlider ? parseFloat(temperatureSlider.value) : 0.7,
      topP: topPSlider ? parseFloat(topPSlider.value) : 0.9,
      model: modelSelect ? modelSelect.value : 'gemini-2.5-flash-preview-05-20'
    };

    await chrome.storage.local.set({
      chefai_advanced_settings: settings
    });

    console.log('Settings saved:', settings);

  } catch (error) {
    console.error('Error saving settings:', error);
  }
}

async function saveApiKey() {
  try {
    const apiKeyInput = document.getElementById('gemini-api-key');
    if (!apiKeyInput) return;

    let apiKey = apiKeyInput.value;
    
    // If it's the masked value, use the real value
    if (apiKey === '••••••••••••••••••••••••••••••' && apiKeyInput.dataset.realValue) {
      apiKey = apiKeyInput.dataset.realValue;
    }

    const config = {
      provider: 'gemini',
      apiKey: apiKey,
      model: 'gemini-2.5-flash-preview-05-20'
    };

    await chrome.storage.local.set({
      chefai_llm_config: config
    });

    console.log('API key saved');

  } catch (error) {
    console.error('Error saving API key:', error);
  }
}

function showAddProviderDialog() {
  const providers = ['OpenRouter', 'OpenAI', 'Anthropic'];
  const provider = prompt(`Add new provider. Choose from:\n${providers.join(', ')}`);
  
  if (provider && providers.includes(provider)) {
    addProviderCard(provider.toLowerCase());
  }
}

function addProviderCard(providerName) {
  const providersContainer = document.getElementById('providers-container') || 
                           document.querySelector('.provider-card').parentNode;
  
  const providerCard = document.createElement('div');
  providerCard.className = 'provider-card';
  providerCard.innerHTML = `
    <div class="provider-header">
      <h3 class="provider-name">${providerName.charAt(0).toUpperCase() + providerName.slice(1)}</h3>
      <button class="delete-btn">Delete</button>
    </div>
    <div class="form-group">
      <label class="form-label">API Key*</label>
      <input type="password" class="form-input" placeholder="Enter your API key">
    </div>
    <div class="form-group">
      <label class="form-label">Models</label>
      <div class="model-tags"></div>
      <div style="margin-top: 12px; font-size: 12px; color: #7f8c8d;">
        Type and Press Enter or Space to add
      </div>
    </div>
  `;

  providersContainer.appendChild(providerCard);
  
  // Re-setup event listeners for the new card
  setupEventListeners();
}

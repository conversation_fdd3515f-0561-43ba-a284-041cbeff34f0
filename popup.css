/* ChefAI Popup Styles */
body {
  width: 380px;
  min-height: 550px;
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
  overflow-x: hidden;
}

.popup-container {
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 20px;
}

.logo {
  font-size: 2em;
  margin-bottom: 5px;
}

.title {
  font-size: 1.2em;
  font-weight: bold;
  margin: 0;
}

.subtitle {
  font-size: 0.9em;
  opacity: 0.8;
  margin: 5px 0 0;
}

.input-section {
  margin: 20px 0;
}

.input-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 0.9em;
}

.input-field {
  width: 100%;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  box-sizing: border-box;
}

.input-field::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.input-field:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
}

.button {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 8px 0;
}

.button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.button.primary {
  background: linear-gradient(45deg, #4CAF50, #45a049);
}

.button.primary:hover {
  background: linear-gradient(45deg, #45a049, #3d8b40);
}

.message {
  padding: 10px;
  border-radius: 6px;
  margin: 10px 0;
  font-size: 0.9em;
  display: none;
}

.message.success {
  background: rgba(76, 175, 80, 0.3);
  border: 1px solid rgba(76, 175, 80, 0.5);
}

.message.error {
  background: rgba(244, 67, 54, 0.3);
  border: 1px solid rgba(244, 67, 54, 0.5);
}

.message.info {
  background: rgba(33, 150, 243, 0.3);
  border: 1px solid rgba(33, 150, 243, 0.5);
}

.recent-recipes {
  margin-top: 20px;
}

.section-title {
  font-size: 1em;
  font-weight: 600;
  margin-bottom: 10px;
  opacity: 0.9;
}

.recent-recipe-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  margin: 6px 0;
  font-size: 0.85em;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recipe-title {
  font-weight: 500;
}

.recipe-time {
  opacity: 0.7;
  font-size: 0.8em;
}

.no-recipes {
  text-align: center;
  opacity: 0.7;
  font-size: 0.9em;
  margin: 10px 0;
}

#recipeDisplay {
  display: none;
  margin-top: 15px;
}

.recipe-card {
  background: rgba(255, 255, 255, 0.15);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.recipe-card h3 {
  margin: 0 0 8px;
  font-size: 1.1em;
}

.recipe-card p {
  margin: 0 0 10px;
  font-size: 0.9em;
  opacity: 0.9;
}

.recipe-meta {
  display: flex;
  gap: 15px;
  font-size: 0.8em;
  opacity: 0.8;
}

/* Enhanced Styles for New Features */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.button.secondary {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.button.secondary:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* Status Section */
.status-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 13px;
  opacity: 0.9;
}

.status-value {
  font-size: 13px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-value.success {
  background: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.status-value.warning {
  background: rgba(255, 152, 0, 0.3);
  color: #ff9800;
}

.status-value.error {
  background: rgba(244, 67, 54, 0.3);
  color: #f44336;
}

.status-value.info {
  background: rgba(33, 150, 243, 0.3);
  color: #2196F3;
}

/* Shortcuts Info */
.shortcuts-info {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  padding: 12px;
  margin-top: 15px;
  text-align: center;
}

.shortcut-text {
  margin: 0;
  font-size: 12px;
  opacity: 0.8;
}

kbd {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 11px;
  font-family: monospace;
  margin: 0 2px;
}

/* Message Styles */
.message {
  padding: 10px;
  border-radius: 6px;
  margin: 10px 0;
  font-size: 13px;
  text-align: center;
  display: none;
}

.message.success {
  background: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
  color: #4CAF50;
}

.message.warning {
  background: rgba(255, 152, 0, 0.2);
  border: 1px solid rgba(255, 152, 0, 0.5);
  color: #ff9800;
}

.message.error {
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.5);
  color: #f44336;
}

.message.info {
  background: rgba(33, 150, 243, 0.2);
  border: 1px solid rgba(33, 150, 243, 0.5);
  color: #2196F3;
}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ChefAI - Recipe Generator</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <div class="header">
      <div class="logo">🍳</div>
      <h1 class="title">ChefAI</h1>
      <p class="subtitle">AI-Powered Recipe Generator</p>
    </div>

    <div id="message" class="message"></div>

    <div class="input-section">
      <label class="input-label" for="ingredientsInput">
        What ingredients do you have?
      </label>
      <input
        type="text"
        id="ingredientsInput"
        class="input-field"
        placeholder="e.g., chicken, rice, vegetables..."
      >
    </div>

    <button id="generateBtn" class="button primary">
      ✨ Generate Recipe
    </button>

    <div id="recipeDisplay"></div>

    <div class="recent-recipes">
      <div class="section-title">Recent Recipes</div>
      <div id="recentRecipes">
        <p class="no-recipes">No recent recipes. Generate your first recipe!</p>
      </div>
    </div>

    <div class="action-buttons">
      <button id="openDashboardBtn" class="button primary">
        📊 Open Dashboard
      </button>

      <button id="openSidebarBtn" class="button secondary">
        📋 Quick Sidebar
      </button>
    </div>

    <div class="status-section">
      <div class="status-item">
        <span class="status-label">🤖 AI Status:</span>
        <span id="aiStatus" class="status-value">Ready</span>
      </div>
      <div class="status-item">
        <span class="status-label">⚙️ Settings:</span>
        <span id="settingsStatus" class="status-value">Configured</span>
      </div>
    </div>

    <div class="shortcuts-info">
      <p class="shortcut-text">💡 Press <kbd>Ctrl+Shift+C</kbd> to toggle interface</p>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>

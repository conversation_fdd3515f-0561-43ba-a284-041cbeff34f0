<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ChefAI Settings</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #2c3e50; color: #ecf0f1; height: 100vh; overflow: hidden;
    }
    .settings-container { display: flex; height: 100vh; }
    .settings-sidebar { width: 200px; background: #34495e; border-right: 1px solid #4a5f7a; padding: 20px 0; }
    .sidebar-header { padding: 0 20px 20px 20px; }
    .sidebar-header h2 { font-size: 18px; font-weight: 600; color: #ecf0f1; }
    .nav-item {
      display: flex; align-items: center; padding: 12px 20px; color: #bdc3c7; font-size: 14px;
      border-left: 3px solid transparent; gap: 10px; transition: all 0.2s ease; cursor: pointer;
    }
    .nav-item:hover { background: rgba(52, 152, 219, 0.1); color: #ecf0f1; }
    .nav-item.active { color: #ecf0f1; border-left-color: #3498db; background: rgba(52, 152, 219, 0.1); }
    .settings-content { flex: 1; padding: 30px; overflow-y: auto; }
    .section-title { font-size: 24px; font-weight: 600; color: #ecf0f1; margin-bottom: 24px; }
    .provider-card { background: #34495e; border-radius: 8px; padding: 24px; border: 1px solid #4a5f7a; margin-bottom: 20px; }
    .provider-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
    .provider-name { font-size: 18px; font-weight: 600; color: #ecf0f1; }
    .delete-btn { background: #e74c3c; border: none; color: white; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; font-weight: 500; }
    .form-group { margin-bottom: 20px; }
    .form-label { display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500; color: #ecf0f1; }
    .form-input { width: 100%; padding: 10px 12px; background: #2c3e50; border: 1px solid #4a5f7a; border-radius: 4px; color: #ecf0f1; font-size: 14px; font-family: monospace; }
    .form-select { width: 100%; padding: 10px 12px; background: #34495e; border: 1px solid #4a5f7a; border-radius: 4px; color: #ecf0f1; font-size: 14px; }
    .model-tags { display: flex; flex-wrap: wrap; gap: 8px; }
    .model-tag { background: #3498db; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; display: inline-flex; align-items: center; gap: 4px; }
    .model-tag button { background: none; border: none; color: white; cursor: pointer; font-size: 14px; padding: 0; margin-left: 4px; }
    .add-provider-btn { background: #3498db; border: none; color: white; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: 500; width: 100%; }
    .model-selection { background: #34495e; border-radius: 8px; padding: 24px; border: 1px solid #4a5f7a; }
    .planner-section { background: #2c3e50; border-radius: 6px; padding: 20px; margin-bottom: 24px; border: 1px solid #34495e; }
    .planner-title { font-size: 16px; font-weight: 600; color: #ecf0f1; margin-bottom: 8px; }
    .planner-description { font-size: 14px; color: #7f8c8d; line-height: 1.4; margin-bottom: 20px; }
    .slider-container { display: flex; align-items: center; gap: 16px; }
    .slider { flex: 1; height: 4px; background: #4a5f7a; border-radius: 2px; outline: none; -webkit-appearance: none; }
    .slider::-webkit-slider-thumb { -webkit-appearance: none; width: 16px; height: 16px; background: #3498db; border-radius: 50%; cursor: pointer; }
    .slider-values { display: flex; gap: 8px; align-items: center; }
    .slider-value { font-size: 14px; color: #bdc3c7; min-width: 40px; }
    .slider-default { font-size: 14px; color: #7f8c8d; }
    .tab-content { display: none; }
    .tab-content.active { display: block; }

    /* Provider Dropdown */
    .provider-dropdown {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: #34495e;
      border: 1px solid #4a5f7a;
      border-radius: 4px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 1000;
      margin-top: 4px;
    }

    .provider-option {
      padding: 12px 16px;
      color: #ecf0f1;
      cursor: pointer;
      font-size: 14px;
      border-bottom: 1px solid #4a5f7a;
    }

    .provider-option:hover {
      background: #4a5f7a;
    }

    .provider-option:last-child {
      border-bottom: none;
    }

    /* Settings Section */
    .settings-section {
      background: #34495e;
      border-radius: 8px;
      padding: 24px;
      border: 1px solid #4a5f7a;
    }

    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #4a5f7a;
    }

    .setting-item:last-child {
      border-bottom: none;
    }

    .setting-header {
      flex: 1;
    }

    .setting-header .form-label {
      margin-bottom: 4px;
      font-size: 16px;
      font-weight: 500;
    }

    .setting-description {
      font-size: 14px;
      color: #7f8c8d;
      line-height: 1.4;
    }

    .setting-input {
      width: 80px;
      padding: 8px 12px;
      background: #2c3e50;
      border: 1px solid #4a5f7a;
      border-radius: 4px;
      color: #ecf0f1;
      font-size: 14px;
      text-align: center;
    }

    /* Toggle Switch */
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #4a5f7a;
      transition: .4s;
      border-radius: 24px;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .toggle-slider {
      background-color: #3498db;
    }

    input:checked + .toggle-slider:before {
      transform: translateX(26px);
    }

    /* Firewall Styles */
    .firewall-section {
      background: #34495e;
      border-radius: 8px;
      padding: 24px;
      border: 1px solid #4a5f7a;
    }

    .firewall-tabs {
      display: flex;
      margin: 20px 0;
      background: #2c3e50;
      border-radius: 4px;
      padding: 4px;
    }

    .firewall-tab-btn {
      flex: 1;
      background: transparent;
      border: none;
      color: #bdc3c7;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s ease;
    }

    .firewall-tab-btn.active {
      background: #3498db;
      color: white;
    }

    .firewall-input-section {
      display: flex;
      gap: 8px;
      margin: 20px 0;
    }

    .firewall-input {
      flex: 1;
      padding: 10px 12px;
      background: #2c3e50;
      border: 1px solid #4a5f7a;
      border-radius: 4px;
      color: #ecf0f1;
      font-size: 14px;
    }

    .firewall-add-btn {
      background: #27ae60;
      border: none;
      color: white;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
    }

    .firewall-info {
      background: #2c3e50;
      border-radius: 6px;
      padding: 20px;
      margin-top: 20px;
      border: 1px solid #34495e;
    }

    /* Firewall Rules */
    .firewall-rules-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .firewall-rule {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #2c3e50;
      padding: 8px 12px;
      border-radius: 4px;
      border: 1px solid #4a5f7a;
    }

    .firewall-rule span {
      color: #ecf0f1;
      font-size: 14px;
    }

    .remove-rule-btn {
      background: #e74c3c;
      border: none;
      color: white;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .remove-rule-btn:hover {
      background: #c0392b;
    }
  </style>
</head>
<body>
  <div class="settings-container">
    <div class="settings-sidebar">
      <div class="sidebar-header"><h2>Settings</h2></div>
      <nav>
        <div class="nav-item" data-tab="general"><span>⚙️</span>General</div>
        <div class="nav-item active" data-tab="models"><span>🤖</span>Models</div>
        <div class="nav-item" data-tab="firewall"><span>🔒</span>Firewall</div>
        <div class="nav-item" data-tab="help"><span>❓</span>Help</div>
      </nav>
    </div>
    <div class="settings-content">
      <div id="general-tab" class="tab-content">
        <h2 class="section-title">General</h2>

        <div class="settings-section">
          <div class="setting-item">
            <div class="setting-header">
              <label class="form-label">Max Steps per Task</label>
              <span class="setting-description">Step limit per task</span>
            </div>
            <input type="number" class="setting-input" value="100" min="1" max="1000">
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="form-label">Max Actions per Step</label>
              <span class="setting-description">Action limit per step</span>
            </div>
            <input type="number" class="setting-input" value="5" min="1" max="50">
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="form-label">Failure Tolerance</label>
              <span class="setting-description">How many consecutive failures before stopping</span>
            </div>
            <input type="number" class="setting-input" value="3" min="1" max="10">
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="form-label">Enable Vision</label>
              <span class="setting-description">Use vision capability of LLMs (consumes more tokens for better results)</span>
            </div>
            <label class="toggle-switch">
              <input type="checkbox" checked>
              <span class="toggle-slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="form-label">Display Highlights</label>
              <span class="setting-description">Show visual highlights on interactive elements (e.g. buttons, links, etc.)</span>
            </div>
            <label class="toggle-switch">
              <input type="checkbox" checked>
              <span class="toggle-slider"></span>
            </label>
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="form-label">Replanning Frequency</label>
              <span class="setting-description">Reconsider and update the plan every [Number] steps</span>
            </div>
            <input type="number" class="setting-input" value="3" min="1" max="20">
          </div>

          <div class="setting-item">
            <div class="setting-header">
              <label class="form-label">Page Load Wait Time</label>
              <span class="setting-description">Minimum wait time after page loads (250-5000ms)</span>
            </div>
            <input type="number" class="setting-input" value="250" min="250" max="5000" step="50">
          </div>
        </div>
      </div>
      <div id="models-tab" class="tab-content active">
        <div style="margin-bottom: 40px;">
          <h2 class="section-title">LLM Providers</h2>
          <div class="provider-card">
            <div class="provider-header">
              <h3 class="provider-name">Gemini</h3>
              <button class="delete-btn">Delete</button>
            </div>
            <div class="form-group">
              <label class="form-label">API Key*</label>
              <input type="password" class="form-input" value="••••••••••••••••••••••••••••••" id="gemini-api-key">
            </div>
            <div class="form-group">
              <label class="form-label">Models</label>
              <div class="model-tags">
                <span class="model-tag">gemini-2.5-flash-preview-05-20<button>×</button></span>
                <span class="model-tag">gemini-2.5-pro-preview-06-05<button>×</button></span>
                <span class="model-tag">gemini-2.0-flash<button>×</button></span>
              </div>
              <div style="margin-top: 12px; font-size: 12px; color: #7f8c8d;">Type and Press Enter or Space to add</div>
            </div>
          </div>

          <!-- Add New Provider Button -->
          <button class="add-provider-btn" id="add-provider-btn">+ Add New Provider</button>

          <!-- Provider Selection Dropdown -->
          <div id="provider-dropdown" class="provider-dropdown" style="display: none;">
            <div class="provider-option" data-provider="openai">OpenAI</div>
            <div class="provider-option" data-provider="anthropic">Anthropic</div>
            <div class="provider-option" data-provider="deepseek">DeepSeek</div>
            <div class="provider-option" data-provider="grok">Grok</div>
            <div class="provider-option" data-provider="ollama">Ollama</div>
            <div class="provider-option" data-provider="azure">Azure OpenAI</div>
            <div class="provider-option" data-provider="openrouter">OpenRouter</div>
            <div class="provider-option" data-provider="groq">Groq</div>
            <div class="provider-option" data-provider="cerebras">Cerebras</div>
            <div class="provider-option" data-provider="openai-compatible">OpenAI-compatible API Provider</div>
          </div>
        </div>

        <div style="margin-bottom: 40px;">
          <h2 class="section-title">Model Selection</h2>
          <div class="model-selection">
            <div class="planner-section">
              <h4 class="planner-title">Planner</h4>
              <p class="planner-description">Develops and refines strategies to complete tasks</p>
              <div class="form-group">
                <label class="form-label">Model</label>
                <select class="form-select" id="model-select">
                  <option value="gemini-2.5-flash-preview-05-20">Gemini > gemini-2.5-flash-preview-05-20</option>
                  <option value="gemini-2.5-pro-preview-06-05">Gemini > gemini-2.5-pro-preview-06-05</option>
                  <option value="gemini-2.0-flash">Gemini > gemini-2.0-flash</option>
                </select>
              </div>
              <div class="form-group">
                <label class="form-label">Temperature</label>
                <div class="slider-container">
                  <input type="range" class="slider" id="temperature-slider" min="0" max="1" step="0.01" value="0.7">
                  <div class="slider-values">
                    <span class="slider-value" id="temperature-value">0.70</span>
                    <span class="slider-default">0.7</span>
                  </div>
                </div>
              </div>
              <div class="form-group">
                <label class="form-label">Top P</label>
                <div class="slider-container">
                  <input type="range" class="slider" id="top-p-slider" min="0" max="1" step="0.01" value="0.9">
                  <div class="slider-values">
                    <span class="slider-value" id="top-p-value">0.900</span>
                    <span class="slider-default">0.9</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="firewall-tab" class="tab-content">
        <h2 class="section-title">Firewall</h2>

        <div class="firewall-section">
          <div class="setting-item">
            <div class="setting-header">
              <label class="form-label">Enable Firewall</label>
            </div>
            <label class="toggle-switch">
              <input type="checkbox" id="enable-firewall" checked>
              <span class="toggle-slider"></span>
            </label>
          </div>

          <div class="firewall-tabs">
            <button class="firewall-tab-btn active" data-tab="allow">Allow List</button>
            <button class="firewall-tab-btn" data-tab="deny">Deny List</button>
          </div>

          <div class="firewall-input-section">
            <input type="text" class="firewall-input" placeholder="Enter domain or URL (e.g. example.com, localhost, 127.0.0.1)">
            <button class="firewall-add-btn">Add</button>
          </div>

          <div class="firewall-status">
            <p style="color: #7f8c8d; font-size: 14px; text-align: center; margin: 20px 0;">
              No domains in allow list. Empty allow list means all non-denied domains are allowed.
            </p>
          </div>

          <div class="firewall-info">
            <h3 style="color: #ecf0f1; margin-bottom: 16px;">How the Firewall Works</h3>
            <ul style="color: #bdc3c7; line-height: 1.6; padding-left: 20px;">
              <li>The firewall contains a deny list and an allow list.</li>
              <li>If both lists are empty, all URLs are allowed</li>
              <li>Deny list takes priority - if a URL matches any deny list entry, it's blocked</li>
              <li>When allow list is empty, all non-denied URLs are allowed</li>
              <li><strong>When allow list is not empty, only matching URLs are allowed</strong></li>
              <li>Wildcards are NOT supported yet</li>
            </ul>
          </div>
        </div>
      </div>
      <div id="help-tab" class="tab-content">
        <h2 class="section-title">Help & Support</h2>
        <div style="color: #bdc3c7; line-height: 1.6;">
          <h3 style="color: #ecf0f1; margin-bottom: 16px;">Getting Started</h3>
          <p style="margin-bottom: 16px;">1. Configure your AI provider by adding an API key<br>2. Select your preferred model and adjust settings<br>3. Use Ctrl+Shift+C to open ChefAI on any webpage</p>
          <h3 style="color: #ecf0f1; margin-bottom: 16px;">Supported Providers</h3>
          <p style="margin-bottom: 16px;">• <strong>Gemini:</strong> Google's advanced AI model<br>• <strong>OpenRouter:</strong> Access to multiple AI models<br>• <strong>OpenAI:</strong> GPT-3.5, GPT-4, and other models<br>• <strong>Anthropic:</strong> Claude models (via OpenRouter)</p>
          <h3 style="color: #ecf0f1; margin-bottom: 16px;">Need Help?</h3>
          <p>Visit our GitHub repository for documentation and support.</p>
        </div>
      </div>
    </div>
  </div>
  <script src="options.js"></script>
</body>
</html>

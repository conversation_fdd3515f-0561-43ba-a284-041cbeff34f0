<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ChefAI Settings</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #2c3e50; color: #ecf0f1; height: 100vh; overflow: hidden;
    }
    .settings-container { display: flex; height: 100vh; }
    .settings-sidebar { width: 200px; background: #34495e; border-right: 1px solid #4a5f7a; padding: 20px 0; }
    .sidebar-header { padding: 0 20px 20px 20px; }
    .sidebar-header h2 { font-size: 18px; font-weight: 600; color: #ecf0f1; }
    .nav-item {
      display: flex; align-items: center; padding: 12px 20px; color: #bdc3c7; font-size: 14px;
      border-left: 3px solid transparent; gap: 10px; transition: all 0.2s ease; cursor: pointer;
    }
    .nav-item:hover { background: rgba(52, 152, 219, 0.1); color: #ecf0f1; }
    .nav-item.active { color: #ecf0f1; border-left-color: #3498db; background: rgba(52, 152, 219, 0.1); }
    .settings-content { flex: 1; padding: 30px; overflow-y: auto; }
    .section-title { font-size: 24px; font-weight: 600; color: #ecf0f1; margin-bottom: 24px; }
    .provider-card { background: #34495e; border-radius: 8px; padding: 24px; border: 1px solid #4a5f7a; margin-bottom: 20px; }
    .provider-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
    .provider-name { font-size: 18px; font-weight: 600; color: #ecf0f1; }
    .delete-btn { background: #e74c3c; border: none; color: white; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; font-weight: 500; }
    .form-group { margin-bottom: 20px; }
    .form-label { display: block; margin-bottom: 8px; font-size: 14px; font-weight: 500; color: #ecf0f1; }
    .form-input { width: 100%; padding: 10px 12px; background: #2c3e50; border: 1px solid #4a5f7a; border-radius: 4px; color: #ecf0f1; font-size: 14px; font-family: monospace; }
    .form-select { width: 100%; padding: 10px 12px; background: #34495e; border: 1px solid #4a5f7a; border-radius: 4px; color: #ecf0f1; font-size: 14px; }
    .model-tags { display: flex; flex-wrap: wrap; gap: 8px; }
    .model-tag { background: #3498db; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; display: inline-flex; align-items: center; gap: 4px; }
    .model-tag button { background: none; border: none; color: white; cursor: pointer; font-size: 14px; padding: 0; margin-left: 4px; }
    .add-provider-btn { background: #3498db; border: none; color: white; padding: 10px 20px; border-radius: 4px; cursor: pointer; font-size: 14px; font-weight: 500; width: 100%; }
    .model-selection { background: #34495e; border-radius: 8px; padding: 24px; border: 1px solid #4a5f7a; }
    .planner-section { background: #2c3e50; border-radius: 6px; padding: 20px; margin-bottom: 24px; border: 1px solid #34495e; }
    .planner-title { font-size: 16px; font-weight: 600; color: #ecf0f1; margin-bottom: 8px; }
    .planner-description { font-size: 14px; color: #7f8c8d; line-height: 1.4; margin-bottom: 20px; }
    .slider-container { display: flex; align-items: center; gap: 16px; }
    .slider { flex: 1; height: 4px; background: #4a5f7a; border-radius: 2px; outline: none; -webkit-appearance: none; }
    .slider::-webkit-slider-thumb { -webkit-appearance: none; width: 16px; height: 16px; background: #3498db; border-radius: 50%; cursor: pointer; }
    .slider-values { display: flex; gap: 8px; align-items: center; }
    .slider-value { font-size: 14px; color: #bdc3c7; min-width: 40px; }
    .slider-default { font-size: 14px; color: #7f8c8d; }
    .tab-content { display: none; }
    .tab-content.active { display: block; }
  </style>
</head>
<body>
  <div class="settings-container">
    <div class="settings-sidebar">
      <div class="sidebar-header"><h2>Settings</h2></div>
      <nav>
        <div class="nav-item active" data-tab="general"><span>⚙️</span>General</div>
        <div class="nav-item" data-tab="models"><span>🤖</span>Models</div>
        <div class="nav-item" data-tab="firewall"><span>🔒</span>Firewall</div>
        <div class="nav-item" data-tab="help"><span>❓</span>Help</div>
      </nav>
    </div>
    <div class="settings-content">
      <div id="general-tab" class="tab-content active">
        <div style="margin-bottom: 40px;">
          <h2 class="section-title">LLM Providers</h2>
          <div class="provider-card">
            <div class="provider-header">
              <h3 class="provider-name">Gemini</h3>
              <button class="delete-btn">Delete</button>
            </div>
            <div class="form-group">
              <label class="form-label">API Key*</label>
              <input type="password" class="form-input" value="••••••••••••••••••••••••••••••" id="gemini-api-key">
            </div>
            <div class="form-group">
              <label class="form-label">Models</label>
              <div class="model-tags">
                <span class="model-tag">gemini-2.5-flash-preview-05-20<button>×</button></span>
                <span class="model-tag">gemini-2.5-pro-preview-06-05<button>×</button></span>
                <span class="model-tag">gemini-2.0-flash<button>×</button></span>
              </div>
              <div style="margin-top: 12px; font-size: 12px; color: #7f8c8d;">Type and Press Enter or Space to add</div>
            </div>
          </div>
          <button class="add-provider-btn">+ Add New Provider</button>
        </div>
        <div style="margin-bottom: 40px;">
          <h2 class="section-title">Model Selection</h2>
          <div class="model-selection">
            <div class="planner-section">
              <h4 class="planner-title">Planner</h4>
              <p class="planner-description">Develops and refines strategies to complete tasks</p>
              <div class="form-group">
                <label class="form-label">Model</label>
                <select class="form-select">
                  <option>Gemini > gemini-2.5-flash-preview-05-20</option>
                  <option>Gemini > gemini-2.5-pro-preview-06-05</option>
                  <option>Gemini > gemini-2.0-flash</option>
                </select>
              </div>
              <div class="form-group">
                <label class="form-label">Temperature</label>
                <div class="slider-container">
                  <input type="range" class="slider" min="0" max="1" step="0.1" value="0.7">
                  <div class="slider-values">
                    <span class="slider-value">0.70</span>
                    <span class="slider-default">0.7</span>
                  </div>
                </div>
              </div>
              <div class="form-group">
                <label class="form-label">Top P</label>
                <div class="slider-container">
                  <input type="range" class="slider" min="0" max="1" step="0.1" value="0.9">
                  <div class="slider-values">
                    <span class="slider-value">0.900</span>
                    <span class="slider-default">0.9</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="models-tab" class="tab-content">
        <h2 class="section-title">Model Management</h2>
        <p style="color: #7f8c8d;">Manage your AI models and configurations here.</p>
      </div>
      <div id="firewall-tab" class="tab-content">
        <h2 class="section-title">Firewall Settings</h2>
        <p style="color: #7f8c8d;">Configure security and access controls.</p>
      </div>
      <div id="help-tab" class="tab-content">
        <h2 class="section-title">Help & Support</h2>
        <div style="color: #bdc3c7; line-height: 1.6;">
          <h3 style="color: #ecf0f1; margin-bottom: 16px;">Getting Started</h3>
          <p style="margin-bottom: 16px;">1. Configure your AI provider by adding an API key<br>2. Select your preferred model and adjust settings<br>3. Use Ctrl+Shift+C to open ChefAI on any webpage</p>
          <h3 style="color: #ecf0f1; margin-bottom: 16px;">Supported Providers</h3>
          <p style="margin-bottom: 16px;">• <strong>Gemini:</strong> Google's advanced AI model<br>• <strong>OpenRouter:</strong> Access to multiple AI models<br>• <strong>OpenAI:</strong> GPT-3.5, GPT-4, and other models<br>• <strong>Anthropic:</strong> Claude models (via OpenRouter)</p>
          <h3 style="color: #ecf0f1; margin-bottom: 16px;">Need Help?</h3>
          <p>Visit our GitHub repository for documentation and support.</p>
        </div>
      </div>
    </div>
  </div>
  <script src="options.js"></script>
</body>
</html>

import { Recipe, Ingredient, CookingStep } from '../types';

export interface LLMProvider {
  id: string;
  name: string;
  description: string;
  apiEndpoint: string;
  models: LLMModel[];
  requiresApiKey: boolean;
  maxTokens: number;
  supportedFeatures: LLMFeature[];
}

export interface LLMModel {
  id: string;
  name: string;
  description: string;
  contextLength: number;
  costPer1kTokens: number;
  capabilities: string[];
}

export type LLMFeature = 'recipe_generation' | 'ingredient_substitution' | 'cooking_tips' | 'meal_planning' | 'dietary_analysis';

export interface LLMConfig {
  provider: string;
  model: string;
  apiKey: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
}

export interface RecipeGenerationRequest {
  ingredients?: string[];
  cuisineType?: string;
  dietaryRestrictions?: string[];
  servings?: number;
  cookingTime?: number;
  difficulty?: string;
  mealType?: string;
  customPrompt?: string;
}

export interface LLMResponse {
  success: boolean;
  data?: any;
  error?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    cost?: number;
  };
}

export class LLMService {
  private static instance: LLMService;
  private config: LLMConfig | null = null;
  private providers: Map<string, LLMProvider> = new Map();

  private constructor() {
    this.initializeProviders();
    this.loadConfiguration();
  }

  public static getInstance(): LLMService {
    if (!LLMService.instance) {
      LLMService.instance = new LLMService();
    }
    return LLMService.instance;
  }

  private initializeProviders(): void {
    const providers: LLMProvider[] = [
      {
        id: 'openrouter',
        name: 'OpenRouter',
        description: 'Access to multiple AI models through OpenRouter API',
        apiEndpoint: 'https://openrouter.ai/api/v1/chat/completions',
        requiresApiKey: true,
        maxTokens: 4096,
        supportedFeatures: ['recipe_generation', 'ingredient_substitution', 'cooking_tips', 'meal_planning', 'dietary_analysis'],
        models: [
          {
            id: 'anthropic/claude-3.5-sonnet',
            name: 'Claude 3.5 Sonnet',
            description: 'Anthropic\'s most capable model for complex reasoning',
            contextLength: 200000,
            costPer1kTokens: 0.003,
            capabilities: ['recipe_generation', 'cooking_analysis', 'meal_planning']
          },
          {
            id: 'openai/gpt-4-turbo',
            name: 'GPT-4 Turbo',
            description: 'OpenAI\'s most capable model with large context',
            contextLength: 128000,
            costPer1kTokens: 0.01,
            capabilities: ['recipe_generation', 'cooking_tips', 'dietary_analysis']
          },
          {
            id: 'google/gemini-pro-1.5',
            name: 'Gemini Pro 1.5',
            description: 'Google\'s advanced multimodal model',
            contextLength: 1000000,
            costPer1kTokens: 0.0025,
            capabilities: ['recipe_generation', 'ingredient_analysis', 'cooking_guidance']
          },
          {
            id: 'meta-llama/llama-3.1-70b-instruct',
            name: 'Llama 3.1 70B',
            description: 'Meta\'s open-source large language model',
            contextLength: 131072,
            costPer1kTokens: 0.0009,
            capabilities: ['recipe_generation', 'cooking_tips']
          }
        ]
      },
      {
        id: 'gemini',
        name: 'Google Gemini',
        description: 'Google\'s native Gemini API for advanced AI capabilities',
        apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models',
        requiresApiKey: true,
        maxTokens: 8192,
        supportedFeatures: ['recipe_generation', 'ingredient_substitution', 'cooking_tips', 'dietary_analysis'],
        models: [
          {
            id: 'gemini-1.5-pro',
            name: 'Gemini 1.5 Pro',
            description: 'Google\'s most capable model with multimodal understanding',
            contextLength: 2000000,
            costPer1kTokens: 0.0035,
            capabilities: ['recipe_generation', 'image_analysis', 'cooking_guidance']
          },
          {
            id: 'gemini-1.5-flash',
            name: 'Gemini 1.5 Flash',
            description: 'Fast and efficient model for quick responses',
            contextLength: 1000000,
            costPer1kTokens: 0.00015,
            capabilities: ['recipe_generation', 'quick_tips']
          }
        ]
      },
      {
        id: 'openai',
        name: 'OpenAI',
        description: 'Direct access to OpenAI\'s GPT models',
        apiEndpoint: 'https://api.openai.com/v1/chat/completions',
        requiresApiKey: true,
        maxTokens: 4096,
        supportedFeatures: ['recipe_generation', 'ingredient_substitution', 'cooking_tips', 'meal_planning'],
        models: [
          {
            id: 'gpt-4o',
            name: 'GPT-4o',
            description: 'OpenAI\'s flagship multimodal model',
            contextLength: 128000,
            costPer1kTokens: 0.005,
            capabilities: ['recipe_generation', 'image_analysis', 'cooking_guidance']
          },
          {
            id: 'gpt-4o-mini',
            name: 'GPT-4o Mini',
            description: 'Smaller, faster version of GPT-4o',
            contextLength: 128000,
            costPer1kTokens: 0.00015,
            capabilities: ['recipe_generation', 'quick_responses']
          }
        ]
      },
      {
        id: 'anthropic',
        name: 'Anthropic',
        description: 'Direct access to Anthropic\'s Claude models',
        apiEndpoint: 'https://api.anthropic.com/v1/messages',
        requiresApiKey: true,
        maxTokens: 4096,
        supportedFeatures: ['recipe_generation', 'ingredient_substitution', 'cooking_tips', 'dietary_analysis'],
        models: [
          {
            id: 'claude-3-5-sonnet-20241022',
            name: 'Claude 3.5 Sonnet',
            description: 'Anthropic\'s most capable model',
            contextLength: 200000,
            costPer1kTokens: 0.003,
            capabilities: ['recipe_generation', 'detailed_analysis', 'cooking_guidance']
          },
          {
            id: 'claude-3-haiku-20240307',
            name: 'Claude 3 Haiku',
            description: 'Fast and efficient model for quick tasks',
            contextLength: 200000,
            costPer1kTokens: 0.00025,
            capabilities: ['recipe_generation', 'quick_tips']
          }
        ]
      }
    ];

    providers.forEach(provider => {
      this.providers.set(provider.id, provider);
    });
  }

  private async loadConfiguration(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['llmConfig']);
      if (result.llmConfig) {
        this.config = JSON.parse(result.llmConfig);
      } else {
        // Set default configuration
        this.config = {
          provider: 'openrouter',
          model: 'anthropic/claude-3.5-sonnet',
          apiKey: '',
          temperature: 0.7,
          maxTokens: 2048,
          systemPrompt: 'You are ChefAI, an expert culinary assistant. Generate detailed, practical recipes with clear instructions, ingredient lists, and cooking tips. Focus on accuracy, safety, and delicious results.'
        };
        await this.saveConfiguration();
      }
    } catch (error) {
      console.error('Failed to load LLM configuration:', error);
    }
  }

  public async saveConfiguration(config?: LLMConfig): Promise<void> {
    if (config) {
      this.config = config;
    }
    
    try {
      await chrome.storage.local.set({
        llmConfig: JSON.stringify(this.config)
      });
    } catch (error) {
      console.error('Failed to save LLM configuration:', error);
    }
  }

  public getConfiguration(): LLMConfig | null {
    return this.config;
  }

  public getProviders(): LLMProvider[] {
    return Array.from(this.providers.values());
  }

  public getProvider(providerId: string): LLMProvider | undefined {
    return this.providers.get(providerId);
  }

  public async generateRecipe(request: RecipeGenerationRequest): Promise<LLMResponse> {
    if (!this.config || !this.config.apiKey) {
      return {
        success: false,
        error: 'LLM configuration not set. Please configure your API key in settings.'
      };
    }

    const provider = this.providers.get(this.config.provider);
    if (!provider) {
      return {
        success: false,
        error: 'Invalid LLM provider configured.'
      };
    }

    try {
      const prompt = this.buildRecipePrompt(request);
      const response = await this.callLLMAPI(prompt);
      
      if (response.success && response.data) {
        const recipe = this.parseRecipeResponse(response.data);
        return {
          success: true,
          data: recipe,
          usage: response.usage
        };
      }
      
      return response;
    } catch (error) {
      console.error('Recipe generation failed:', error);
      return {
        success: false,
        error: `Failed to generate recipe: ${error.message}`
      };
    }
  }

  private buildRecipePrompt(request: RecipeGenerationRequest): string {
    let prompt = 'You are ChefAI, an expert culinary assistant. Generate a detailed, practical recipe with the following requirements:\n\n';

    if (request.ingredients && request.ingredients.length > 0) {
      prompt += `🥘 Available ingredients: ${request.ingredients.join(', ')}\n`;
      prompt += `Focus on creating a recipe that primarily uses these ingredients.\n`;
    }

    if (request.cuisineType) {
      prompt += `🌍 Cuisine type: ${request.cuisineType}\n`;
    }

    if (request.dietaryRestrictions && request.dietaryRestrictions.length > 0) {
      prompt += `🥗 Dietary restrictions: ${request.dietaryRestrictions.join(', ')}\n`;
      prompt += `Ensure the recipe strictly adheres to these dietary requirements.\n`;
    }

    if (request.servings) {
      prompt += `👥 Servings: ${request.servings} people\n`;
    }

    if (request.cookingTime) {
      prompt += `⏱️ Maximum cooking time: ${request.cookingTime} minutes\n`;
    }

    if (request.difficulty) {
      prompt += `📊 Difficulty level: ${request.difficulty}\n`;
    }

    if (request.mealType) {
      prompt += `🍽️ Meal type: ${request.mealType}\n`;
    }

    if (request.customPrompt) {
      prompt += `✨ Additional requirements: ${request.customPrompt}\n`;
    }

    prompt += `\n📋 IMPORTANT: Provide the recipe in the following exact JSON format (no additional text before or after):

{
  "title": "Recipe Name",
  "description": "Brief, appetizing description of the dish",
  "servings": ${request.servings || 4},
  "prepTime": number (in minutes),
  "cookTime": number (in minutes),
  "difficulty": "${request.difficulty || 'Medium'}",
  "cuisine": "${request.cuisineType || 'International'}",
  "ingredients": [
    {
      "name": "ingredient name",
      "amount": number,
      "unit": "unit of measurement (cups, tbsp, tsp, oz, lbs, etc.)",
      "category": "protein|vegetable|grain|dairy|spice|other"
    }
  ],
  "instructions": [
    {
      "step": number,
      "description": "Clear, detailed instruction with specific techniques",
      "duration": number (in minutes, 0 if not applicable)
    }
  ],
  "tips": [
    "Professional cooking tip 1",
    "Professional cooking tip 2",
    "Professional cooking tip 3"
  ],
  "nutrition": {
    "calories": number (per serving),
    "protein": number (grams),
    "carbs": number (grams),
    "fat": number (grams),
    "fiber": number (grams),
    "sugar": number (grams),
    "sodium": number (mg)
  }
}

🎯 Requirements:
- Use precise measurements and standard cooking units
- Provide clear, step-by-step instructions
- Include professional cooking tips
- Calculate realistic nutritional values
- Ensure the recipe is practical and delicious
- Focus on ingredient efficiency and flavor balance`;

    return prompt;
  }

  private async callLLMAPI(prompt: string): Promise<LLMResponse> {
    if (!this.config) {
      return { success: false, error: 'No configuration available' };
    }

    const provider = this.providers.get(this.config.provider);
    if (!provider) {
      return { success: false, error: 'Provider not found' };
    }

    try {
      let response: Response;
      let requestBody: any;

      switch (this.config.provider) {
        case 'openrouter':
          requestBody = {
            model: this.config.model,
            messages: [
              { role: 'system', content: this.config.systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature: this.config.temperature,
            max_tokens: this.config.maxTokens
          };

          response = await fetch(provider.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.config.apiKey}`,
              'HTTP-Referer': chrome.runtime.getURL(''),
              'X-Title': 'ChefAI Extension'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        case 'gemini':
          const geminiUrl = `${provider.apiEndpoint}/${this.config.model}:generateContent?key=${this.config.apiKey}`;
          requestBody = {
            contents: [{
              parts: [{
                text: `${this.config.systemPrompt}\n\n${prompt}`
              }]
            }],
            generationConfig: {
              temperature: this.config.temperature,
              maxOutputTokens: this.config.maxTokens
            }
          };

          response = await fetch(geminiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        case 'openai':
          requestBody = {
            model: this.config.model,
            messages: [
              { role: 'system', content: this.config.systemPrompt },
              { role: 'user', content: prompt }
            ],
            temperature: this.config.temperature,
            max_tokens: this.config.maxTokens
          };

          response = await fetch(provider.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.config.apiKey}`
            },
            body: JSON.stringify(requestBody)
          });
          break;

        case 'anthropic':
          requestBody = {
            model: this.config.model,
            max_tokens: this.config.maxTokens,
            temperature: this.config.temperature,
            system: this.config.systemPrompt,
            messages: [
              { role: 'user', content: prompt }
            ]
          };

          response = await fetch(provider.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': this.config.apiKey,
              'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify(requestBody)
          });
          break;

        default:
          return { success: false, error: 'Unsupported provider' };
      }

      if (!response.ok) {
        const errorText = await response.text();
        return {
          success: false,
          error: `API request failed: ${response.status} ${response.statusText} - ${errorText}`
        };
      }

      const data = await response.json();
      return this.parseAPIResponse(data, this.config.provider);

    } catch (error) {
      console.error('LLM API call failed:', error);
      return {
        success: false,
        error: `Network error: ${error.message}`
      };
    }
  }

  private parseAPIResponse(data: any, provider: string): LLMResponse {
    try {
      let content: string;
      let usage: any = {};

      switch (provider) {
        case 'openrouter':
        case 'openai':
          content = data.choices[0].message.content;
          usage = {
            promptTokens: data.usage?.prompt_tokens || 0,
            completionTokens: data.usage?.completion_tokens || 0,
            totalTokens: data.usage?.total_tokens || 0
          };
          break;

        case 'gemini':
          content = data.candidates[0].content.parts[0].text;
          usage = {
            promptTokens: data.usageMetadata?.promptTokenCount || 0,
            completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
            totalTokens: data.usageMetadata?.totalTokenCount || 0
          };
          break;

        case 'anthropic':
          content = data.content[0].text;
          usage = {
            promptTokens: data.usage?.input_tokens || 0,
            completionTokens: data.usage?.output_tokens || 0,
            totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
          };
          break;

        default:
          return { success: false, error: 'Unknown provider response format' };
      }

      return {
        success: true,
        data: content,
        usage
      };

    } catch (error) {
      return {
        success: false,
        error: `Failed to parse API response: ${error.message}`
      };
    }
  }

  private parseRecipeResponse(content: string): Recipe | null {
    try {
      // Clean the content and extract JSON
      let cleanContent = content.trim();

      // Remove markdown code blocks if present
      cleanContent = cleanContent.replace(/```json\s*/g, '').replace(/```\s*/g, '');

      // Extract JSON from the response - look for the first complete JSON object
      const jsonMatch = cleanContent.match(/\{[\s\S]*?\}(?=\s*$|\s*\n\s*[^}])/);
      if (!jsonMatch) {
        // Try to find JSON between curly braces
        const startIndex = cleanContent.indexOf('{');
        const lastIndex = cleanContent.lastIndexOf('}');
        if (startIndex !== -1 && lastIndex !== -1 && lastIndex > startIndex) {
          cleanContent = cleanContent.substring(startIndex, lastIndex + 1);
        } else {
          throw new Error('No valid JSON found in response');
        }
      } else {
        cleanContent = jsonMatch[0];
      }

      const recipeData = JSON.parse(cleanContent);

      // Validate required fields
      if (!recipeData.title) {
        throw new Error('Recipe title is missing');
      }

      // Convert to our Recipe format with enhanced validation
      const recipe: Recipe = {
        id: `llm-recipe-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        title: recipeData.title.trim(),
        description: recipeData.description?.trim() || 'AI-generated recipe',
        servings: Math.max(1, parseInt(recipeData.servings) || 4),
        prepTime: Math.max(0, parseInt(recipeData.prepTime) || 15),
        cookTime: Math.max(0, parseInt(recipeData.cookTime) || 30),
        difficulty: ['Easy', 'Medium', 'Hard'].includes(recipeData.difficulty) ? recipeData.difficulty : 'Medium',
        cuisine: recipeData.cuisine?.trim() || 'International',
        ingredients: this.parseIngredients(recipeData.ingredients || []),
        instructions: this.parseInstructions(recipeData.instructions || []),
        tags: Array.isArray(recipeData.tags) ? recipeData.tags.filter(tag => typeof tag === 'string') : [],
        nutrition: this.parseNutrition(recipeData.nutrition),
        tips: Array.isArray(recipeData.tips) ? recipeData.tips.filter(tip => typeof tip === 'string').slice(0, 5) : [],
        createdAt: new Date(),
        source: 'AI Generated'
      };

      return recipe;

    } catch (error) {
      console.error('Failed to parse recipe response:', error);
      console.error('Content that failed to parse:', content);
      return null;
    }
  }

  private parseIngredients(ingredients: any[]): any[] {
    if (!Array.isArray(ingredients)) return [];

    return ingredients.map((ing: any, index: number) => ({
      id: `ing-${index}-${Date.now()}`,
      name: ing.name?.trim() || `Ingredient ${index + 1}`,
      amount: parseFloat(ing.amount) || 1,
      unit: ing.unit?.trim() || 'unit',
      category: ['protein', 'vegetable', 'grain', 'dairy', 'spice', 'other'].includes(ing.category?.toLowerCase())
        ? ing.category.toLowerCase()
        : 'other'
    })).filter(ing => ing.name && ing.name !== `Ingredient ${ingredients.indexOf(ing) + 1}`);
  }

  private parseInstructions(instructions: any[]): any[] {
    if (!Array.isArray(instructions)) return [];

    return instructions.map((inst: any, index: number) => ({
      step: parseInt(inst.step) || index + 1,
      description: inst.description?.trim() || `Step ${index + 1}`,
      duration: Math.max(0, parseInt(inst.duration) || 0)
    })).filter(inst => inst.description && inst.description !== `Step ${instructions.indexOf(inst) + 1}`);
  }

  private parseNutrition(nutrition: any): any {
    if (!nutrition || typeof nutrition !== 'object') {
      return {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
        sugar: 0,
        sodium: 0
      };
    }

    return {
      calories: Math.max(0, parseInt(nutrition.calories) || 0),
      protein: Math.max(0, parseFloat(nutrition.protein) || 0),
      carbs: Math.max(0, parseFloat(nutrition.carbs) || 0),
      fat: Math.max(0, parseFloat(nutrition.fat) || 0),
      fiber: Math.max(0, parseFloat(nutrition.fiber) || 0),
      sugar: Math.max(0, parseFloat(nutrition.sugar) || 0),
      sodium: Math.max(0, parseFloat(nutrition.sodium) || 0)
    };
  }

  public async testConnection(): Promise<LLMResponse> {
    if (!this.config || !this.config.apiKey) {
      return {
        success: false,
        error: 'No API key configured'
      };
    }

    try {
      const testPrompt = 'Respond with "Connection successful" if you can read this message.';
      const response = await this.callLLMAPI(testPrompt);
      
      if (response.success) {
        return {
          success: true,
          data: 'Connection test successful',
          usage: response.usage
        };
      }
      
      return response;
    } catch (error) {
      return {
        success: false,
        error: `Connection test failed: ${error.message}`
      };
    }
  }

  public async getIngredientSubstitutions(ingredient: string, dietary_restrictions?: string[]): Promise<LLMResponse> {
    const prompt = `Suggest 3-5 substitutions for "${ingredient}" in cooking${dietary_restrictions ? ` considering these dietary restrictions: ${dietary_restrictions.join(', ')}` : ''}. 
    
    Provide the response in JSON format:
    {
      "ingredient": "${ingredient}",
      "substitutions": [
        {
          "name": "substitute name",
          "ratio": "1:1 or specific ratio",
          "notes": "any important notes about taste/texture changes"
        }
      ]
    }`;

    return await this.callLLMAPI(prompt);
  }

  public async getCookingTips(recipe_title: string, difficulty?: string): Promise<LLMResponse> {
    const prompt = `Provide 3-5 professional cooking tips for making "${recipe_title}"${difficulty ? ` (${difficulty} level)` : ''}. Focus on technique, timing, and common mistakes to avoid.
    
    Provide the response in JSON format:
    {
      "recipe": "${recipe_title}",
      "tips": [
        {
          "category": "technique|timing|ingredient|safety",
          "tip": "detailed cooking tip",
          "importance": "high|medium|low"
        }
      ]
    }`;

    return await this.callLLMAPI(prompt);
  }
}

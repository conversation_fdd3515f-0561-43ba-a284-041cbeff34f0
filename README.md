# 🍳 ChefAI - Advanced AI Recipe Generator

A sophisticated Chrome extension featuring a modern sidebar interface and comprehensive settings dashboard. Generate personalized recipes using cutting-edge AI models with professional-grade configuration options.

## ✨ Features

### 🤖 Advanced AI Integration
- **Multiple LLM Support**: OpenRouter API, Gemini API, OpenAI, Anthropic
- **Smart Recipe Generation**: Context-aware recipe creation based on user preferences
- **Real-time Updates**: Live synchronization across tabs and sessions
- **Intelligent Suggestions**: AI-powered ingredient and cooking method recommendations

### 🎨 Modern Interface
- **Glassmorphism Design**: Beautiful translucent interface with blur effects
- **Right Sidebar Integration**: Non-intrusive sliding sidebar from browser's right side
- **Responsive Design**: Optimized for all screen sizes
- **Dark/Light Theme Support**: Automatic theme adaptation
- **Customizable UI**: Extensive personalization options

### ⚙️ Advanced Settings Dashboard
- **LLM Configuration**: Easy setup and management of AI providers
- **Target Audience Settings**: Family, adults, children, elderly, etc.
- **Recipe Complexity Control**: Simple to gourmet level recipes
- **Cultural Preferences**: Mediterranean, Asian, Middle Eastern, and more
- **Dietary Profiles**: Vegetarian, vegan, keto, gluten-free, etc.
- **Kitchen Equipment**: Customize based on available appliances
- **Time & Budget Constraints**: Flexible recipe generation parameters

### 🔧 Customization & Personalization
- **Profile Management**: Multiple customization profiles
- **Behavior Settings**: Auto-save, smart suggestions, contextual help
- **UI Customization**: Sidebar position, animations, tooltips
- **Keyboard Shortcuts**: Configurable hotkeys for quick access
- **Custom CSS**: Advanced styling options for power users

### 📊 Performance & Analytics
- **Performance Monitoring**: Real-time performance metrics
- **Caching System**: Intelligent caching for faster load times
- **Memory Optimization**: Efficient resource management
- **Error Handling**: Robust error recovery and reporting

## 🚀 Installation

### From Chrome Web Store (Coming Soon)
1. Visit the Chrome Web Store
2. Search for "ChefAI Recipe Generator"
3. Click "Add to Chrome"

### Manual Installation (Development)
1. Clone this repository:
   ```bash
   git clone https://github.com/your-username/chefai-extension.git
   cd chefai-extension
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the extension:
   ```bash
   npm run build
   ```

4. Load in Chrome:
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode"
   - Click "Load unpacked" and select the `dist` folder

## 🔑 API Setup

### OpenRouter API
1. Visit [OpenRouter](https://openrouter.ai/)
2. Create an account and get your API key
3. Open ChefAI Dashboard → LLM Configuration
4. Select "OpenRouter" as provider
5. Enter your API key

### Gemini API
1. Visit [Google AI Studio](https://makersuite.google.com/)
2. Get your Gemini API key
3. Open ChefAI Dashboard → LLM Configuration
4. Select "Gemini" as provider
5. Enter your API key

### OpenAI API
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Get your API key
3. Open ChefAI Dashboard → LLM Configuration
4. Select "OpenAI" as provider
5. Enter your API key

## 🎯 Usage

### Quick Start
1. Click the 🍳 floating button on any webpage
2. Configure your AI provider in the dashboard
3. Set your preferences in Advanced Settings
4. Start generating personalized recipes!

### Keyboard Shortcuts
- `Ctrl+Shift+C`: Toggle ChefAI interface
- `Ctrl+Enter`: Generate recipe (when in generator)
- `Ctrl+S`: Save current recipe
- `Escape`: Close interface

### Dashboard Navigation
- **Recipe Generator**: Main recipe creation interface
- **LLM Settings**: Configure AI models and providers
- **Advanced Settings**: Detailed preferences and options
- **Customization**: Personalize interface and behavior

## 🏗️ Architecture

### Core Services
- **LLMService**: Manages AI model interactions
- **AdvancedSettingsService**: Handles user preferences
- **RecipeService**: Recipe storage and management
- **RealtimeUpdateService**: Live synchronization
- **CustomizationService**: UI personalization
- **APIManager**: Unified API request handling

### Components
- **DashboardInterface**: Main dashboard component
- **SidebarInterface**: Recipe generation sidebar
- **AdvancedSettingsPanel**: Detailed settings management
- **CustomizationPanel**: UI personalization options
- **RealtimeNotifications**: Live update notifications

### Performance Features
- **Lazy Loading**: Components loaded on demand
- **Intelligent Caching**: Smart data caching system
- **Memory Management**: Automatic cleanup and optimization
- **Bundle Splitting**: Optimized code delivery

## 🧪 Testing

Run the integration test suite:
```bash
npm run test
```

The test suite covers:
- Service initialization
- LLM provider functionality
- Settings management
- Recipe operations
- Real-time updates
- API communication
- End-to-end workflows

## 🔧 Development

### Project Structure
```
src/
├── components/          # React components
├── services/           # Core business logic
├── content/           # Content scripts
├── styles/            # CSS stylesheets
├── utils/             # Utility functions
├── tests/             # Test suites
└── types/             # TypeScript definitions
```

### Build Commands
```bash
npm run build          # Production build
npm run dev            # Development build
npm run watch          # Watch mode
npm run test           # Run tests
npm run lint           # Code linting
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if needed
5. Submit a pull request

## 📝 Configuration

### Advanced Settings
The extension supports extensive configuration through the Advanced Settings panel:

- **Target Audience**: Customize recipes for specific groups
- **Recipe Complexity**: Control ingredient count and difficulty
- **Cultural Preferences**: Select preferred cuisines
- **Dietary Restrictions**: Set dietary requirements
- **Cooking Methods**: Specify preferred cooking techniques
- **Kitchen Equipment**: List available appliances
- **Time Constraints**: Set cooking time preferences
- **Budget Range**: Control ingredient cost levels

### Customization Profiles
Create and manage multiple customization profiles:
- **Theme Settings**: Colors, fonts, animations
- **UI Preferences**: Layout, positioning, behavior
- **Keyboard Shortcuts**: Custom hotkey assignments
- **Performance Settings**: Caching, optimization options

## 🔒 Privacy & Security

- **Local Storage**: All data stored locally in browser
- **No Data Collection**: No personal information sent to servers
- **API Security**: Secure API key management
- **Content Security**: CSP-compliant implementation

## 🐛 Troubleshooting

### Common Issues
1. **API Key Not Working**: Verify key is correct and has sufficient credits
2. **Interface Not Loading**: Check if extension is enabled
3. **Slow Performance**: Clear cache in Customization settings
4. **Recipe Not Saving**: Check browser storage permissions

### Debug Mode
Enable debug mode in Advanced Settings → Behavior Settings for detailed logging.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Support

- **Issues**: [GitHub Issues](https://github.com/your-username/chefai-extension/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/chefai-extension/discussions)
- **Email**: <EMAIL>

## 🎉 Acknowledgments

- OpenRouter for AI model access
- Google for Gemini API
- OpenAI for GPT models
- Anthropic for Claude models
- The open-source community for inspiration and tools

---

**Made with ❤️ for cooking enthusiasts and AI lovers**

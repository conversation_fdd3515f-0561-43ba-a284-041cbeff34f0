// Enhanced Content Script - Integrates ChefAI Dashboard with web pages
class EnhancedChefAIInjector {
  constructor() {
    this.state = {
      isVisible: false,
      isInitialized: false,
      container: null,
      floatingButton: null,
      currentMode: 'dashboard'
    };
    
    this.init();
  }

  async init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupInterface());
    } else {
      this.setupInterface();
    }

    // Listen for messages from extension
    this.setupMessageListeners();
    
    // Setup keyboard shortcuts
    this.setupKeyboardShortcuts();
  }

  setupInterface() {
    try {
      this.createFloatingButton();
      this.loadStyles();
      this.state.isInitialized = true;
      console.log('ChefAI interface initialized successfully');
    } catch (error) {
      console.error('Failed to setup ChefAI interface:', error);
    }
  }

  createFloatingButton() {
    // Remove existing button if any
    if (this.state.floatingButton) {
      this.state.floatingButton.remove();
    }

    this.state.floatingButton = document.createElement('div');
    this.state.floatingButton.id = 'chefai-floating-button';
    this.state.floatingButton.innerHTML = '🍳';
    this.state.floatingButton.title = 'Open ChefAI Dashboard (Ctrl+Shift+C)';

    // Apply enhanced styles
    Object.assign(this.state.floatingButton.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      width: '64px',
      height: '64px',
      borderRadius: '50%',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
      color: 'white',
      fontSize: '28px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      zIndex: '9999',
      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)',
      border: '2px solid rgba(255, 255, 255, 0.2)',
      backdropFilter: 'blur(10px)',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      userSelect: 'none',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    });

    // Add hover effects
    this.state.floatingButton.addEventListener('mouseenter', () => {
      if (this.state.floatingButton) {
        Object.assign(this.state.floatingButton.style, {
          transform: 'scale(1.1) rotate(5deg)',
          boxShadow: '0 12px 35px rgba(0, 0, 0, 0.4)',
          background: 'linear-gradient(135deg, #7c8df0 0%, #8a5ab2 50%, #f5a3fc 100%)'
        });
      }
    });

    this.state.floatingButton.addEventListener('mouseleave', () => {
      if (this.state.floatingButton) {
        Object.assign(this.state.floatingButton.style, {
          transform: 'scale(1) rotate(0deg)',
          boxShadow: '0 8px 25px rgba(0, 0, 0, 0.3)',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)'
        });
      }
    });

    // Add click handler
    this.state.floatingButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.toggleInterface();
    });

    // Add to page
    document.body.appendChild(this.state.floatingButton);
  }

  loadStyles() {
    const stylesheets = [
      'src/styles/sidebar.css',
      'src/styles/dashboard.css',
      'src/styles/customization.css'
    ];

    stylesheets.forEach(href => {
      if (!document.querySelector(`link[href*="${href}"]`)) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = chrome.runtime.getURL(href);
        document.head.appendChild(link);
      }
    });
  }

  setupMessageListeners() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      try {
        switch (message.action) {
          case 'toggleInterface':
            this.toggleInterface();
            sendResponse({ success: true });
            break;
          case 'showInterface':
            this.showInterface(message.mode || 'dashboard');
            sendResponse({ success: true });
            break;
          case 'hideInterface':
            this.hideInterface();
            sendResponse({ success: true });
            break;
          case 'switchMode':
            this.switchMode(message.mode);
            sendResponse({ success: true });
            break;
          case 'getStatus':
            sendResponse({
              success: true,
              status: {
                isVisible: this.state.isVisible,
                isInitialized: this.state.isInitialized,
                currentMode: this.state.currentMode
              }
            });
            break;
          default:
            sendResponse({ success: false, error: 'Unknown action' });
        }
      } catch (error) {
        console.error('Error handling message:', error);
        sendResponse({ success: false, error: error.message });
      }
    });
  }

  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl+Shift+C to toggle interface
      if (e.ctrlKey && e.shiftKey && e.code === 'KeyC') {
        e.preventDefault();
        this.toggleInterface();
      }
      
      // Escape to close interface
      if (e.code === 'Escape' && this.state.isVisible) {
        e.preventDefault();
        this.hideInterface();
      }
    });
  }

  toggleInterface() {
    if (this.state.isVisible) {
      this.hideInterface();
    } else {
      this.showInterface();
    }
  }

  showInterface(mode = 'dashboard') {
    if (!this.state.isInitialized) {
      console.warn('ChefAI interface not initialized');
      return;
    }

    try {
      // Create container if it doesn't exist
      if (!this.state.container) {
        this.createContainer();
      }

      // Update mode
      this.state.currentMode = mode;

      // Create simple interface
      this.renderSimpleInterface();

      // Show interface
      this.state.isVisible = true;
      
      // Hide floating button when interface is open
      if (this.state.floatingButton) {
        this.state.floatingButton.style.display = 'none';
      }

      // Add body class for styling
      document.body.classList.add('chefai-interface-open');

    } catch (error) {
      console.error('Failed to show ChefAI interface:', error);
    }
  }

  hideInterface() {
    try {
      if (this.state.container) {
        this.state.container.remove();
        this.state.container = null;
      }

      this.state.isVisible = false;

      // Show floating button
      if (this.state.floatingButton) {
        this.state.floatingButton.style.display = 'flex';
      }

      // Remove body class
      document.body.classList.remove('chefai-interface-open');

    } catch (error) {
      console.error('Failed to hide ChefAI interface:', error);
    }
  }

  switchMode(mode) {
    if (this.state.currentMode !== mode) {
      this.state.currentMode = mode;
      if (this.state.isVisible) {
        this.renderSimpleInterface();
      }
    }
  }

  createContainer() {
    this.state.container = document.createElement('div');
    this.state.container.id = 'chefai-app-container';
    this.state.container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10000;
      pointer-events: auto;
    `;
    document.body.appendChild(this.state.container);
  }

  renderSimpleInterface() {
    if (!this.state.container) return;

    // Create a simple interface for now
    this.state.container.innerHTML = `
      <div style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90%;
        max-width: 1200px;
        height: 80%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
        color: white;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      ">
        <div style="
          padding: 30px;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          display: flex;
          justify-content: space-between;
          align-items: center;
        ">
          <h1 style="margin: 0; font-size: 2em; font-weight: 700;">🍳 ChefAI Dashboard</h1>
          <button id="chefai-close-btn" style="
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            font-size: 1.5em;
            cursor: pointer;
            padding: 10px 15px;
            border-radius: 50%;
            transition: all 0.3s ease;
          ">×</button>
        </div>
        
        <div style="
          flex: 1;
          padding: 40px;
          text-align: center;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        ">
          <div style="
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
          ">
            <h2 style="margin: 0 0 20px 0; font-size: 1.5em;">Welcome to ChefAI!</h2>
            <p style="margin: 0 0 30px 0; line-height: 1.6; opacity: 0.9;">
              Your advanced AI-powered recipe generator is ready to create personalized recipes 
              based on your preferences, dietary requirements, and available ingredients.
            </p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0;">
              <div style="
                background: rgba(255, 255, 255, 0.1);
                padding: 20px;
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
              ">
                <div style="font-size: 2em; margin-bottom: 10px;">🤖</div>
                <h3 style="margin: 0 0 10px 0; font-size: 1.1em;">Multiple AI Models</h3>
                <p style="margin: 0; font-size: 0.9em; opacity: 0.8;">OpenRouter, Gemini, OpenAI, Anthropic</p>
              </div>
              
              <div style="
                background: rgba(255, 255, 255, 0.1);
                padding: 20px;
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
              ">
                <div style="font-size: 2em; margin-bottom: 10px;">⚙️</div>
                <h3 style="margin: 0 0 10px 0; font-size: 1.1em;">Advanced Settings</h3>
                <p style="margin: 0; font-size: 0.9em; opacity: 0.8;">Personalized preferences</p>
              </div>
              
              <div style="
                background: rgba(255, 255, 255, 0.1);
                padding: 20px;
                border-radius: 10px;
                border: 1px solid rgba(255, 255, 255, 0.2);
              ">
                <div style="font-size: 2em; margin-bottom: 10px;">🎨</div>
                <h3 style="margin: 0 0 10px 0; font-size: 1.1em;">Beautiful Interface</h3>
                <p style="margin: 0; font-size: 0.9em; opacity: 0.8;">Glassmorphism design</p>
              </div>
            </div>
            
            <div style="margin-top: 30px;">
              <p style="margin: 0 0 20px 0; font-size: 0.9em; opacity: 0.8;">
                To get started, configure your AI provider in the settings and start generating amazing recipes!
              </p>
              
              <button id="chefai-get-started" style="
                background: linear-gradient(45deg, #4CAF50, #45a049);
                border: none;
                color: white;
                padding: 15px 30px;
                font-size: 1.1em;
                font-weight: 600;
                border-radius: 10px;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
              ">
                🚀 Get Started
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
        z-index: -1;
      "></div>
    `;

    // Add event listeners
    const closeBtn = this.state.container.querySelector('#chefai-close-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hideInterface());
    }

    const getStartedBtn = this.state.container.querySelector('#chefai-get-started');
    if (getStartedBtn) {
      getStartedBtn.addEventListener('click', () => {
        // For now, just show a message
        alert('ChefAI is ready! Configure your AI provider in the extension popup to start generating recipes.');
      });
    }
  }

  // Public methods for external access
  getStatus() {
    return {
      isVisible: this.state.isVisible,
      isInitialized: this.state.isInitialized,
      currentMode: this.state.currentMode
    };
  }

  destroy() {
    try {
      this.hideInterface();
      
      if (this.state.floatingButton) {
        this.state.floatingButton.remove();
        this.state.floatingButton = null;
      }

      document.body.classList.remove('chefai-interface-open');
      
      this.state.isInitialized = false;
    } catch (error) {
      console.error('Failed to destroy ChefAI injector:', error);
    }
  }
}

// Initialize the injector
let chefAIInjector = null;

// Only initialize if we're in a valid context
if (typeof window !== 'undefined' && typeof document !== 'undefined') {
  chefAIInjector = new EnhancedChefAIInjector();
}

// Export for potential external access
if (typeof window !== 'undefined') {
  window.ChefAIInjector = chefAIInjector;
}
